# 🤖 AI Assistant Quick Reference - Kitco Research AI

## 📋 Project Summary
**Kitco Research AI** is a Flask-based web application that performs AI-powered research using LLMs and web searches. The project has been recently reorganized for better maintainability.

## 🏗️ Current Project Structure (Post-Reorganization)
```
kitco_research_ai/
├── docs/                    # 📚 All documentation (moved here)
├── scripts/                 # 🔧 Shell scripts (moved here)
├── config/                  # ⚙️ Config files (moved here)
├── src/kitco_research_ai/ # 💻 Main source code
├── data/                    # 🗄️ Database (ldr.db)
├── app.py                   # 🎯 Main entry point
├── setup/start/restart      # 🛠️ Convenience scripts
└── README.md                # 📖 Quick start guide
```

## 🚀 Key Entry Points & Commands

### Application Entry
- **Main**: `app.py` (default port: 8765)
- **Quick Start**: `./start [port]`
- **Setup**: `./setup`
- **Restart**: `./restart [port]`

### Important Paths
- **Database**: `data/ldr.db` (SQLite)
- **Config**: `config/requirements.txt`, `config/pyproject.toml`
- **Documentation**: `docs/README.md` (complete guide)
- **Source**: `src/kitco_research_ai/`

## 🔧 Common Operations

### Development
```bash
source .venv/bin/activate    # Activate virtual environment
./start                      # Start development server
./restart                    # Fresh restart with cleanup
```

### Configuration
- **Environment**: `.env` file in root
- **Dependencies**: `config/requirements.txt`
- **Project Config**: `config/pyproject.toml`

### Testing
```bash
python -c "import sys; sys.path.insert(0, 'src'); from kitco_research_ai.web.app import main"
```

## 🗄️ Database Information
- **Type**: SQLite
- **Location**: `data/ldr.db`
- **Tables**: research_history, research_logs, research_resources, app_settings
- **Auto-migration**: Yes, handled automatically

## 🔌 LLM Providers Supported
1. **OpenAI** (API key required)
2. **LM Studio** (local, auto-detected)
3. **Ollama** (local, auto-detected)
4. **Custom Endpoints** (OpenAI-compatible)

## 📁 Key Source Files
- **Web App**: `src/kitco_research_ai/web/app.py`
- **Database**: `src/kitco_research_ai/web/models/database.py`
- **Config**: `src/kitco_research_ai/config/llm_config.py`
- **Utilities**: `src/kitco_research_ai/utilities/db_utils.py`

## 🛠️ Recent Changes (Important for AI Context)
1. **Reorganized** all documentation to `docs/`
2. **Moved** shell scripts to `scripts/`
3. **Centralized** config files in `config/`
4. **Consolidated** database to `data/ldr.db`
5. **Updated** all file references and paths
6. **Created** convenience scripts in root

## ⚠️ Important Notes for AI Assistants
- **Database path** was recently moved from `src/data/` to `data/`
- **All scripts** now reference new file locations
- **Documentation** is now in `docs/` directory
- **Configuration** files are in `config/` directory
- **Default port** is 8765 (user preference)
- **Package management** should use pip/package managers, not manual editing

## 🔍 Troubleshooting Quick Fixes
- **Port in use**: `./restart [different_port]`
- **Missing deps**: `./setup` or `pip install -r config/requirements.txt`
- **Database issues**: Check `data/` directory exists and is writable
- **Import errors**: Ensure virtual environment is activated

## 📚 Documentation Hierarchy
1. **Quick Start**: `README.md` (root)
2. **Complete Guide**: `docs/README.md`
3. **Architecture**: `docs/ARCHITECTURE.md`
4. **This Reference**: `docs/COMPREHENSIVE_PROJECT_DOCUMENTATION.md`
5. **AI Reference**: `docs/AI_ASSISTANT_QUICK_REFERENCE.md`

## 🎯 User Preferences (From Memory)
- **Default port**: 8765
- **Clean codebase**: Remove unnecessary files
- **Robust structure**: Maintainable and logical organization
- **Production-ready**: Comprehensive error checking
- **app.py as main entry**: Primary application entry point

---
**Use this reference to quickly understand the project structure and make informed decisions about code changes.**
