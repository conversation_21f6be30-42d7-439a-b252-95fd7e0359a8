# Research Modes Guide

This document explains the different research modes available in Kitco Research AI and their characteristics.

## Overview

Kitco Research AI offers two primary research modes, each optimized for different use cases and time constraints:

- **Quick Mode**: Fast, efficient research for immediate insights
- **Detailed Mode**: Comprehensive, thorough research for in-depth analysis

## Quick Mode

### Purpose
Quick mode is designed for rapid research when you need immediate insights or preliminary information on a topic.

### Characteristics
- **Time Limit**: 15 minutes maximum
- **Stall Detection**: 3 minutes (configurable via `general.quick_research_stall_seconds`)
- **Progress Scaling**: 
  - Output generation phase starts at 85% progress
  - Final 15% reserved for synthesis and formatting
- **Search Strategy**: Focused, targeted searches with fewer iterations
- **Output**: Concise summary with key findings

### Best Use Cases
- Breaking news research
- Quick fact-checking
- Preliminary topic exploration
- Time-sensitive research requests
- Initial market overviews

### Progress Behavior
```
0-85%:   Search and analysis phases
85-95%:  Output generation and synthesis
95-100%: Final formatting and completion
```

## Detailed Mode

### Purpose
Detailed mode provides comprehensive, thorough research suitable for in-depth analysis and formal reports.

### Characteristics
- **Time Limit**: 45 minutes maximum
- **Stall Detection**: 7 minutes (configurable via `general.detailed_research_stall_seconds`)
- **Progress Scaling**:
  - Output generation phase capped at 80% progress
  - Report generation phase: 80-95% progress
  - Final 5% for completion tasks
- **Search Strategy**: Multiple iterations with comprehensive source analysis
- **Output**: Detailed report with extensive citations and analysis

### Best Use Cases
- Academic research
- Market analysis reports
- Investment research
- Policy analysis
- Comprehensive competitive intelligence
- Due diligence research

### Progress Behavior
```
0-80%:   Search, analysis, and knowledge accumulation
80-95%:  Report generation and detailed synthesis
95-100%: Final formatting, citations, and completion
```

## Configuration

### Stall Detection Thresholds

The system monitors research progress and automatically terminates stalled processes. These thresholds are configurable:

#### Quick Mode
- **Default**: 180 seconds (3 minutes)
- **Setting**: `general.quick_research_stall_seconds`
- **Description**: Time without progress before termination

#### Detailed Mode
- **Default**: 420 seconds (7 minutes)
- **Setting**: `general.detailed_research_stall_seconds`
- **Description**: Time without progress before termination

### Modifying Stall Thresholds

Stall thresholds can be configured through the application settings interface or by directly editing the configuration files:

#### Via Settings Interface
1. Navigate to Settings → General Settings
2. Adjust "Quick Research Stall Threshold (seconds)" (default: 180)
3. Adjust "Detailed Research Stall Threshold (seconds)" (default: 420)
4. Save changes

#### Via Configuration Files
Update `config/app_config.json`:

```json
{
  "application": {
    "general": {
      "quick_research_stall_seconds": 180,
      "detailed_research_stall_seconds": 420
    }
  }
}
```

#### Database Settings
The settings are stored in the database with keys:
- `general.quick_research_stall_seconds`
- `general.detailed_research_stall_seconds`

**Recommendations**:
- **Quick Mode**: 120-300 seconds (2-5 minutes)
- **Detailed Mode**: 300-600 seconds (5-10 minutes)
- **Production**: Use longer thresholds for stability
- **Development**: Use shorter thresholds for faster feedback

## API Usage

### Starting Research

Both modes use the same API endpoint with different mode parameters:

```javascript
// Quick research
const response = await fetch('/research/api/start_research', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        query: "Your research query",
        mode: "quick"  // or "detailed"
    })
});
```

### Mode Validation

The API validates mode parameters:
- Accepts: `"quick"`, `"detailed"` (case-insensitive)
- Rejects: Invalid modes with HTTP 400 error
- Default: `"quick"` if not specified

## Performance Considerations

### Quick Mode
- **Memory Usage**: Lower due to limited context accumulation
- **CPU Usage**: Moderate with focused searches
- **Network Usage**: Minimal with targeted queries
- **Storage**: Smaller output files

### Detailed Mode
- **Memory Usage**: Higher due to extensive context accumulation
- **CPU Usage**: High with multiple search iterations
- **Network Usage**: Significant with comprehensive searches
- **Storage**: Larger output files with detailed reports

## Monitoring and Troubleshooting

### Progress Monitoring
- Monitor progress via WebSocket events or polling
- Progress percentages are scaled differently per mode
- Stall detection prevents infinite hanging

### Common Issues
1. **Stalled Research**: Automatically terminated after threshold
2. **Timeout Errors**: Check network connectivity and API availability
3. **Memory Issues**: Reduce context limits for detailed mode
4. **Slow Performance**: Consider using quick mode for time-sensitive tasks

### Logging
The system logs important events:
- Research start/completion
- Mode selection and validation
- Stall detection and cleanup
- Progress scaling adjustments

## Best Practices

### Choosing the Right Mode
- Use **Quick Mode** for:
  - Time-sensitive requests
  - Preliminary research
  - Simple fact-checking
  - Resource-constrained environments

- Use **Detailed Mode** for:
  - Comprehensive analysis
  - Formal reports
  - Academic research
  - Investment decisions

### Optimization Tips
1. **Query Formulation**: Clear, specific queries work better
2. **Resource Management**: Monitor system resources during detailed research
3. **Timeout Handling**: Implement proper error handling for timeouts
4. **Progress Tracking**: Use progress events for user feedback

## Future Enhancements

Planned improvements include:
- Custom mode configurations
- Adaptive timeout based on query complexity
- Hybrid modes combining quick and detailed approaches
- User-defined progress scaling rules
