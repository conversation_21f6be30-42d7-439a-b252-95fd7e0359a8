# Centralized Configuration System

## Overview

The Kitco Research AI project now uses a clean, centralized configuration system with just **two files**:

1. **`config/app_config.json`** - Current active configuration (what the app uses)
2. **`config/default_config.json`** - Default/backup configuration (for reset/fallback)

## Key Features

- ✅ **Automatic Fallback**: If current config is corrupted, automatically falls back to defaults
- ✅ **Reset Functionality**: Easy reset to defaults when needed
- ✅ **Backup System**: Automatic backup creation before changes
- ✅ **Validation**: Built-in configuration validation
- ✅ **Editorial/Technical Separation**: Clear separation between user-visible and technical settings
- ✅ **Migration Support**: Automatic migration from old configuration files

## File Structure

```
config/
├── app_config.json          # Current active configuration
├── default_config.json      # Default/backup configuration  
└── backups/                 # Automatic backups
    ├── app_config_20250530_133238.json
    └── old_complete_app_config.json_20250530_133238.json
```

## Configuration Sections

### Editorial Team Settings (Visible to Editorial Team)
- **Application**: Notifications, UI theme, language preferences
- **Search**: Language, time period, region, safe search
- **LLM**: Creativity level, output length preferences
- **Reports**: All report generation settings
- **UI Customization**: Company branding, colors

### Technical Settings (Hidden from Editorial Team)
- **Application**: Debug mode, host, port, timeouts
- **LLM**: Provider, model, API keys, technical parameters
- **Search**: Search engines, iterations, result limits
- **Security**: Session management, rate limiting, encryption
- **Database**: Connection settings, backup configuration
- **Logging**: Log levels, file paths, categories
- **Search Engines**: Individual engine configurations

## Usage

### Basic Configuration Access

```python
from config.config_loader import config

# Get any setting using dot notation
port = config.get('application.technical_settings.port')
theme = config.get('application.editorial_team_settings.ui_theme')
llm_provider = config.get('llm.technical_settings.provider')

# Get with fallback default
timeout = config.get('search.technical_settings.timeout', 30)
```

### Editorial vs Technical Settings

```python
# Get only editorial team settings
editorial_settings = config.get_editorial_settings()

# Get only technical settings (admin/developer)
technical_settings = config.get_technical_settings()

# Get safe summary (no sensitive data)
summary = config.get_config_summary()
```

### Configuration Management

```python
# Reload configuration from files
config.reload()

# Reset to defaults
config.reset_to_defaults()

# Create backup
config.backup_current_config()

# Validate configuration
issues = config.validate()
if issues:
    print("Configuration issues:", issues)
```

## Command Line Management

Use the centralized configuration management script:

```bash
# Check status of configuration files
python3 scripts/config_centralized.py status

# Validate configuration files
python3 scripts/config_centralized.py validate

# Reset current config to defaults
python3 scripts/config_centralized.py reset

# Create backup of current config
python3 scripts/config_centralized.py backup

# Migrate from old configuration system
python3 scripts/config_centralized.py migrate
```

## Configuration Editing

### For Developers
Edit `config/app_config.json` directly and restart the application.

### For Editorial Team
Use the web interface settings page which only shows editorial team settings.

### For Production
1. Make changes to `config/app_config.json`
2. Validate: `python3 scripts/config_centralized.py validate`
3. Backup: `python3 scripts/config_centralized.py backup`
4. Restart application

## Automatic Features

### Fallback Protection
If `config/app_config.json` is corrupted or missing:
1. System automatically detects the issue
2. Logs the error
3. Creates new `app_config.json` from `default_config.json`
4. Application continues running

### Validation on Startup
The system automatically validates configuration on startup and logs any issues:
- Missing required sections
- Invalid port numbers
- Out-of-range values
- Empty API keys in production mode

## Migration from Old System

The old configuration files have been automatically migrated:
- `config/complete_app_config.json` → backed up and removed
- `default_settings.json` → backed up and removed
- All settings consolidated into the new two-file system

## Best Practices

1. **Always backup** before making changes: `python3 scripts/config_centralized.py backup`
2. **Validate** after changes: `python3 scripts/config_centralized.py validate`
3. **Use defaults** as reference - don't edit `default_config.json` directly
4. **Test changes** in development before production
5. **Keep backups** of working configurations

## Troubleshooting

### Configuration Corrupted
```bash
# Reset to defaults
python3 scripts/config_centralized.py reset
```

### Application Won't Start
```bash
# Check configuration status
python3 scripts/config_centralized.py status

# Validate configuration
python3 scripts/config_centralized.py validate
```

### Need to Restore Previous Version
```bash
# Check available backups
ls config/backups/

# Manually copy backup to current config
cp config/backups/app_config_TIMESTAMP.json config/app_config.json
```

## Security Notes

- API keys and sensitive data are in technical settings (hidden from editorial team)
- Configuration files should have appropriate file permissions
- Backup files may contain sensitive data - secure accordingly
- Use environment variables for sensitive values in production

## Support

For issues with the centralized configuration system:
1. Check logs for configuration validation warnings
2. Run `python3 scripts/config_centralized.py status`
3. Validate with `python3 scripts/config_centralized.py validate`
4. Reset to defaults if needed: `python3 scripts/config_centralized.py reset`
