# Production Deployment Guide

## Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Set `SECRET_KEY` environment variable
- [ ] Configure API keys in environment variables
- [ ] Set up production database
- [ ] Configure logging levels
- [ ] Set up monitoring and alerting

### 2. Security Configuration
- [ ] Review and tighten CSP policy
- [ ] Enable HTTPS
- [ ] Configure secure session cookies
- [ ] Set up rate limiting
- [ ] Review file permissions

### 3. Performance Optimization
- [ ] Configure database connection pooling
- [ ] Set up caching (Redis/Memcached)
- [ ] Optimize static file serving
- [ ] Configure load balancing if needed

### 4. Database Migration
```bash
# Backup existing database
cp data/ldr.db data/ldr.db.backup

# Run migrations
python -m src.kitco_research_ai.web.database.schema_upgrade
```

### 5. Dependency Management
```bash
# Install minimal production dependencies (recommended for production)
pip install -r config/requirements-prod.txt

# Alternative: Install full feature set
pip install -r config/requirements.txt

# Verify no security vulnerabilities
pip-audit

# Check for dependency conflicts
pip check
```

**Production Requirements Options:**
- `config/requirements-prod.txt` - **Recommended**: Minimal dependencies for security
- `config/requirements.txt` - Full feature set with all dependencies
- See `config/README-requirements.md` for detailed information

## Deployment Options

### Option 1: Docker Deployment

**Using provided Dockerfiles:**
```bash
# Production deployment (recommended)
docker build -f Dockerfile -t kitco-research-ai:prod .
docker run -p 8765:8765 -v $(pwd)/data:/app/data kitco-research-ai:prod

# Development deployment
docker build -f Dockerfile.dev -t kitco-research-ai:dev .
docker run -p 8765:8765 -v $(pwd):/app kitco-research-ai:dev

# Using Docker Compose
docker-compose --profile prod up    # Production
docker-compose --profile dev up     # Development
```

**Custom Dockerfile (minimal example):**
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY config/requirements-prod.txt .
RUN pip install -r requirements-prod.txt

COPY . .
EXPOSE 8765

CMD ["python", "app.py", "--port=8765", "--host=0.0.0.0"]
```

### Option 2: Systemd Service
```ini
[Unit]
Description=Kitco Research AI
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/kitco-research-ai
Environment=PATH=/opt/kitco-research-ai/venv/bin
ExecStart=/opt/kitco-research-ai/venv/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### Option 3: Nginx + Gunicorn
```bash
# Install Gunicorn
pip install gunicorn

# Run with Gunicorn
gunicorn --bind 0.0.0.0:5001 --workers 4 app:app
```

## Monitoring and Maintenance

### 1. Health Checks
```python
@app.route('/health')
def health_check():
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': __version__
    })
```

### 2. Log Monitoring
- Monitor application logs for errors
- Set up log rotation
- Configure centralized logging

### 3. Performance Monitoring
- Monitor CPU and memory usage
- Track database performance
- Monitor API response times

### 4. Backup Strategy
```bash
# Daily database backup
0 2 * * * cp /opt/kitco-research-ai/data/ldr.db /backups/ldr-$(date +\%Y\%m\%d).db

# Weekly cleanup of old backups
0 3 * * 0 find /backups -name "ldr-*.db" -mtime +30 -delete
```

## Troubleshooting

### Common Issues
1. **Database locked**: Check for long-running transactions
2. **High memory usage**: Monitor search operations and caching
3. **Slow responses**: Check database queries and external API calls
4. **Connection errors**: Verify network connectivity and API endpoints

### Performance Tuning
1. **Database optimization**: Add indexes for frequently queried columns
2. **Caching**: Implement Redis for search result caching
3. **Connection pooling**: Use SQLAlchemy connection pooling
4. **Static files**: Use CDN for static assets

## Security Monitoring
- Monitor failed login attempts
- Track API usage patterns
- Set up intrusion detection
- Regular security audits
