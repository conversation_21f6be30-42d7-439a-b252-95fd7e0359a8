# Secure Secrets Management for Kitco Research AI

This document describes the comprehensive secure secrets management system implemented in Kitco Research AI, designed to handle API keys, passwords, and other sensitive configuration data safely in both development and production environments.

## Overview

The secure secrets management system provides:

- **Environment-based secret handling** with automatic development/production detection
- **AES-256 encryption** for production secret storage
- **Environment variable integration** with `.env` file support
- **Secure defaults** and validation
- **Secret rotation** capabilities
- **Production-ready security** with best practices enforcement

## Architecture

### Components

1. **SecureSecretsManager** (`config/secrets_manager.py`)
   - Core secrets management with encryption
   - Environment detection and mode switching
   - Secret validation and rotation

2. **EnvironmentLoader** (`config/environment.py`)
   - Environment variable loading with type safety
   - `.env` file support with validation
   - Development vs production handling

3. **Enhanced Configuration System** (`config/config_loader.py`)
   - Integrated secret resolution
   - Environment variable overrides
   - Secure caching (excludes secrets)

4. **Security Tools**
   - `scripts/generate_secrets.py` - Generate secure keys
   - `scripts/security_audit.py` - Comprehensive security auditing

## Development Setup

### 1. Initial Setup

```bash
# Copy environment template
cp .env.example .env

# Generate secure secrets
python scripts/generate_secrets.py --output-env

# Edit .env with your actual API keys
nano .env
```

### 2. Environment File Structure

The `.env` file should contain:

```bash
# Core Application
SECRET_KEY=your-generated-secret-key
ENVIRONMENT=development
DEBUG=true

# LLM API Keys
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key

# Search Engine API Keys
BRAVE_API_KEY=BSA-your-brave-key
SERPAPI_API_KEY=your-serpapi-key
GOOGLE_PSE_API_KEY=your-google-pse-key

# Server Configuration
HOST=127.0.0.1
PORT=8765
```

### 3. File Permissions

Ensure proper permissions for security:

```bash
# Set restrictive permissions on .env file
chmod 600 .env

# Run security audit
python scripts/security_audit.py --verbose
```

## Production Deployment

### 1. Environment Setup

```bash
# Set production environment
export ENVIRONMENT=production

# Generate production encryption key
python scripts/generate_secrets.py --production

# Set the encryption key (store securely)
export KITCO_SECRETS_KEY=your-base64-encryption-key
```

### 2. Secret Storage

In production, secrets are stored encrypted:

- **Environment variables** for the encryption key
- **Encrypted file** (`config/.secrets.enc`) for API keys
- **No `.env` files** in production

### 3. Production Checklist

```bash
# Run production security audit
python scripts/security_audit.py --production --verbose

# Validate all secrets
python scripts/generate_secrets.py --validate

# Check file permissions
python scripts/security_audit.py --fix-permissions
```

## Usage Examples

### Accessing Secrets in Code

```python
from config.config_loader import get_secret_safe, get_api_key

# Get any secret
api_key = get_secret_safe('OPENAI_API_KEY')

# Get provider-specific API key
openai_key = get_api_key('openai')
anthropic_key = get_api_key('anthropic')

# Check if running in production
from config.config_loader import is_production_mode
if is_production_mode():
    # Production-specific logic
    pass
```

### Configuration Access

```python
from config.config_loader import config

# Regular configuration (with secret resolution)
llm_provider = config.get('llm.technical_settings.provider')
api_key = config.get('llm.technical_settings.openai_api_key')  # Automatically resolved

# Environment variable override
port = config.get('application.technical_settings.port')  # Can be overridden by PORT env var
```

### Secret Management

```python
from config.secrets_manager import secrets_manager

# Set a new secret
secrets_manager.set_secret('NEW_API_KEY', 'your-secret-value')

# Rotate an existing secret
new_key = secrets_manager.rotate_secret('OPENAI_API_KEY')

# Validate all secrets
issues = secrets_manager.validate_secrets()
if issues:
    print("Security issues found:", issues)
```

## Security Features

### Encryption

- **AES-256 encryption** using Fernet (cryptographically secure)
- **Key derivation** from environment-specific data in development
- **Explicit key management** in production
- **Memory-safe** secret handling

### Environment Detection

Automatic production mode detection based on:
- `ENVIRONMENT=production`
- `FLASK_ENV=production`
- `NODE_ENV=production`
- `PRODUCTION=true`
- Absence of debug flags
- Missing `.git` directory

### Validation

Comprehensive validation includes:
- **Required secrets** presence checking
- **Weak secret** detection (default values, short keys)
- **Production security** requirements
- **Environment consistency** checks

### Best Practices Enforcement

- **No hardcoded secrets** in configuration files
- **Environment variable references** only
- **Secure file permissions** (600 for sensitive files)
- **Production HTTPS** requirement
- **Debug mode** restrictions in production

## Security Commands

### Generate Secrets

```bash
# Generate all secrets for development
python scripts/generate_secrets.py --output-env

# Generate production secrets
python scripts/generate_secrets.py --production --output-env

# Rotate a specific secret
python scripts/generate_secrets.py --rotate SECRET_KEY

# Save to file
python scripts/generate_secrets.py --save-to-file .env.new
```

### Security Auditing

```bash
# Basic security check
python scripts/security_audit.py

# Production audit
python scripts/security_audit.py --production

# Fix permissions automatically
python scripts/security_audit.py --fix-permissions

# Verbose output with export
python scripts/security_audit.py --verbose --export-report security_report.json
```

### Validation

```bash
# Validate current configuration
python scripts/generate_secrets.py --validate

# Check specific components
python -c "from config.secrets_manager import validate_secrets; print(validate_secrets())"
```

## Troubleshooting

### Common Issues

1. **"KITCO_SECRETS_KEY environment variable required in production"**
   - Generate encryption key: `python scripts/generate_secrets.py --production`
   - Set environment variable: `export KITCO_SECRETS_KEY=your-key`

2. **"API key is empty in production mode"**
   - Ensure API keys are set in encrypted storage or environment variables
   - Run: `python scripts/generate_secrets.py --validate`

3. **"Insecure permissions on .env file"**
   - Fix permissions: `chmod 600 .env`
   - Or run: `python scripts/security_audit.py --fix-permissions`

4. **"Secret validation issues found"**
   - Check for default/placeholder values in secrets
   - Ensure minimum key lengths (16+ characters)
   - Verify production-appropriate values

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
export DEBUG=true
python app.py
```

This will show detailed information about:
- Configuration loading
- Secret resolution
- Environment variable overrides
- Security validation

## Migration from Existing Setup

If you have an existing configuration:

1. **Backup current configuration**
   ```bash
   cp config/app_config.json config/app_config.json.backup
   ```

2. **Generate .env file**
   ```bash
   python scripts/generate_secrets.py --output-env > .env
   ```

3. **Update API keys in .env**
   - Replace placeholder values with actual API keys
   - Remove any hardcoded keys from configuration files

4. **Validate setup**
   ```bash
   python scripts/security_audit.py --verbose
   ```

5. **Test application**
   ```bash
   python app.py
   ```

## Security Considerations

### Development

- Use `.env` files for local development
- Never commit `.env` files to version control
- Use different API keys for development/production
- Regularly rotate development keys

### Production

- Use encrypted secret storage
- Store encryption key separately from application
- Enable HTTPS for all communications
- Monitor for secret leaks in logs
- Implement secret rotation schedule
- Use environment-specific configurations

### Monitoring

- Regular security audits
- Access log monitoring
- Secret usage tracking
- Automated vulnerability scanning
- Configuration drift detection

## Support

For security-related issues:

1. Run comprehensive audit: `python scripts/security_audit.py --production --verbose`
2. Check validation: `python scripts/generate_secrets.py --validate`
3. Review logs for security warnings
4. Consult this documentation for best practices

Remember: **Security is a shared responsibility**. Always follow best practices and keep secrets secure!
