# 🔬 Kitco Research AI - Comprehensive Project Documentation

## 📋 Project Overview

**Kitco Research AI** is an AI-powered research assistant that performs deep, iterative analysis using Large Language Models (LLMs) and web searches. The application provides a web interface for conducting comprehensive research on any topic with multiple analysis modes.

### 🎯 Core Functionality
- **Deep Research**: Multi-step iterative research with web searches
- **Quick Research**: Fast single-pass research
- **Web Interface**: User-friendly Flask-based web application
- **Research History**: Persistent storage and tracking of all research
- **Multiple LLM Support**: OpenAI, LM Studio, Ollama, and custom endpoints
- **Real-time Progress**: Live updates during research execution

## 🏗️ Project Architecture

### Directory Structure
```
kitco_research_ai/
├── docs/                          # 📚 All documentation
│   ├── README.md                  # Complete user documentation
│   ├── ARCHITECTURE.md            # Technical architecture details
│   ├── PRODUCTION.md              # Production deployment guide
│   ├── SECURITY.md                # Security configuration
│   ├── PORT_CONFIGURATION.md     # Port configuration details
│   ├── RESTRUCTURING_SUMMARY.md  # Project restructuring history
│   └── COMPREHENSIVE_PROJECT_DOCUMENTATION.md  # This file
├── scripts/                       # 🔧 Shell scripts for operations
│   ├── setup.sh                  # Initial project setup
│   ├── start_app.sh              # Application startup
│   ├── restart.sh                # Fresh restart with cleanup
│   ├── status.sh                 # Project status check
│   └── help.sh                   # Help and reference
├── config/                        # ⚙️ Configuration files
│   ├── pyproject.toml            # Python project configuration
│   ├── requirements.txt          # Python dependencies
│   ├── MANIFEST.in               # Package manifest
│   └── Dockerfile                # Docker configuration
├── src/                          # 💻 Source code
│   └── kitco_research_ai/      # Main package
│       ├── config/               # Configuration modules
│       ├── defaults/             # Default configurations
│       ├── utilities/            # Utility functions
│       ├── web/                  # Web application
│       └── __init__.py
├── tests/                        # 🧪 Test suite
├── data/                         # 🗄️ Application data
│   └── ldr.db                    # SQLite database
├── research_outputs/             # 📊 Generated research reports
├── .venv/                        # 🐍 Python virtual environment
├── app.py                        # 🎯 Main application entry point
├── README.md                     # 📖 Quick start guide
├── LICENSE                       # 📄 MIT License
├── .env                          # 🔐 Environment variables
├── setup                         # 🛠️ Convenience setup script
├── start                         # ▶️ Convenience start script
└── restart                       # 🔄 Convenience restart script
```

### 🔧 Core Components

#### 1. Web Application (`src/kitco_research_ai/web/`)
- **Flask Application**: Main web framework
- **Routes**: API endpoints and web pages
- **Templates**: Jinja2 HTML templates
- **Static Files**: CSS, JavaScript, images
- **Models**: Database models and schemas

#### 2. Configuration System (`src/kitco_research_ai/config/`)
- **LLM Configuration**: Multi-provider LLM setup
- **Environment Management**: .env file handling
- **Settings Management**: Persistent application settings

#### 3. Research Engine (`src/kitco_research_ai/utilities/`)
- **Web Search**: DuckDuckGo integration
- **Content Processing**: Text extraction and analysis
- **Report Generation**: Markdown report creation
- **Database Operations**: SQLite database management

## 🚀 Application Entry Points

### Primary Entry Point
- **File**: `app.py` (root directory)
- **Purpose**: Main application launcher
- **Default Port**: 8765
- **Usage**: `python app.py [--port PORT]`

### Convenience Scripts
- **Setup**: `./setup` → `./scripts/setup.sh`
- **Start**: `./start [port]` → `./scripts/start_app.sh`
- **Restart**: `./restart [port]` → `./scripts/restart.sh`

## 🗄️ Database Schema

### Tables
1. **research_history**: Main research records
2. **research_logs**: Detailed progress logs
3. **research_resources**: Web resources and citations
4. **app_settings**: Application configuration

### Database Location
- **Path**: `data/ldr.db`
- **Type**: SQLite
- **Auto-creation**: Yes, with schema migrations

## 🔌 LLM Provider Support

### Supported Providers
1. **OpenAI**: GPT models via API
2. **LM Studio**: Local model serving
3. **Ollama**: Local model management
4. **Custom Endpoints**: Any OpenAI-compatible API

### Configuration
- **File**: `.env` (environment variables)
- **Settings**: Stored in database
- **Auto-detection**: Available providers detected at startup

## 📊 Research Modes

The application supports two research modes with distinct characteristics and use cases:

### 1. Quick Research Mode

**Purpose**: Fast research for immediate insights and preliminary information.

**Characteristics**:
- **Expected Duration**: 5-15 minutes
- **Time Limit**: 15 minutes maximum (configurable via `general.quick_research_time_minutes`)
- **Stall Detection**: 3 minutes without progress (configurable via `general.quick_research_stall_seconds`)
- **Progress Scaling**:
  - 0-85%: Search and analysis phases
  - 85-95%: Output generation and synthesis
  - 95-100%: Final formatting and completion
- **Search Strategy**: Focused, targeted searches with fewer iterations
- **Output**: Concise summary with key findings

**Best Use Cases**:
- Breaking news research
- Quick fact-checking
- Preliminary topic exploration
- Time-sensitive research requests

### 2. Detailed Research Mode

**Purpose**: Comprehensive, thorough research suitable for in-depth analysis and formal reports.

**Characteristics**:
- **Expected Duration**: 20-45 minutes
- **Time Limit**: 45 minutes maximum (configurable via `general.detailed_research_time_minutes`)
- **Stall Detection**: 7 minutes without progress (configurable via `general.detailed_research_stall_seconds`)
- **Progress Scaling**:
  - 0-80%: Search, analysis, and knowledge accumulation
  - 80-95%: Report generation and detailed synthesis
  - 95-100%: Final formatting, citations, and completion
- **Search Strategy**: Multiple iterations with comprehensive source analysis
- **Output**: Detailed report with extensive citations and analysis

**Best Use Cases**:
- Academic research
- Market analysis reports
- Investment research
- Policy analysis
- Comprehensive competitive intelligence

### Configuration Settings

Research mode behavior can be customized through the following settings:

#### Time Limits
- `general.quick_research_time_minutes`: Maximum time for quick research (default: 15 minutes)
- `general.detailed_research_time_minutes`: Maximum time for detailed research (default: 45 minutes)

#### Stall Detection Thresholds
- `general.quick_research_stall_seconds`: Time without progress before quick research is terminated (default: 180 seconds)
- `general.detailed_research_stall_seconds`: Time without progress before detailed research is terminated (default: 420 seconds)

For detailed information about research modes, see [docs/RESEARCH_MODES.md](RESEARCH_MODES.md).

## 🛠️ Development Workflow

### Initial Setup
```bash
./setup                    # Run initial setup
source .venv/bin/activate  # Activate virtual environment
```

### Development Commands
```bash
./start                    # Start development server
./restart                  # Fresh restart
./scripts/status.sh        # Check project status
./scripts/help.sh          # Show help
```

### Testing
```bash
python -m pytest tests/   # Run test suite
```

## 🔐 Security Configuration

### Environment Variables
- **OPENAI_API_KEY**: OpenAI API access
- **CUSTOM_LLM_ENDPOINT**: Custom LLM endpoint
- **SECRET_KEY**: Flask session security

### Security Features
- **Input Validation**: All user inputs validated
- **SQL Injection Protection**: Parameterized queries
- **XSS Protection**: Template escaping
- **CSRF Protection**: Flask-WTF integration

## 📈 Production Deployment

### Requirements
- **Python**: 3.8+
- **Memory**: 2GB+ recommended
- **Storage**: 1GB+ for database and reports
- **Network**: Internet access for web searches

### Deployment Options
1. **Direct Python**: Run with production WSGI server
2. **Docker**: Use provided Dockerfile
3. **Cloud**: Deploy to cloud platforms

## 🔧 Configuration Management

### Configuration Files
- **pyproject.toml**: Python project metadata
- **requirements.txt**: Python dependencies
- **.env**: Environment variables
- **Database settings**: Stored in SQLite

### Default Settings
- **Port**: 8765
- **Debug**: False (production)
- **Database**: Auto-created SQLite
- **Research Output**: `research_outputs/` directory

## 📝 API Endpoints

### Web Interface
- **GET /**: Main research interface
- **GET /history**: Research history
- **GET /settings**: Configuration page

### API Endpoints
- **POST /api/research**: Start new research
- **GET /api/research/{id}**: Get research status
- **GET /api/research/{id}/logs**: Get research logs

## 🐛 Troubleshooting

### Common Issues
1. **Port in use**: Use `./restart [different_port]`
2. **Missing dependencies**: Run `./setup`
3. **Database errors**: Check `data/` directory permissions
4. **LLM connection**: Verify API keys in `.env`

### Log Locations
- **Application logs**: Console output
- **Research logs**: Database (`research_logs` table)
- **Error logs**: Application console

## 📚 Documentation Files

### User Documentation
- **README.md**: Quick start guide
- **docs/README.md**: Complete user manual
- **docs/PRODUCTION.md**: Production deployment
- **docs/SECURITY.md**: Security configuration

### Technical Documentation
- **docs/ARCHITECTURE.md**: Technical architecture
- **docs/PORT_CONFIGURATION.md**: Port configuration
- **This file**: Comprehensive project documentation

## 🔄 Version History

### Recent Changes
- **Project Reorganization**: Improved directory structure
- **Database Consolidation**: Unified database location
- **Script Organization**: Centralized shell scripts
- **Documentation Restructure**: Organized documentation

### Maintenance
- **Dependencies**: Regularly updated in `config/requirements.txt`
- **Security**: Regular security updates
- **Database**: Automatic schema migrations

## 🎯 Key Design Principles

1. **Modularity**: Clear separation of concerns
2. **Maintainability**: Well-organized code structure
3. **Usability**: Simple setup and operation
4. **Reliability**: Robust error handling
5. **Scalability**: Designed for growth
6. **Security**: Security-first approach

## 🔍 Detailed Implementation Guide

### Source Code Structure

#### Web Application (`src/kitco_research_ai/web/`)
```
web/
├── __init__.py
├── app.py                 # Flask application factory
├── app_factory.py         # Application configuration
├── routes/                # Route handlers
│   ├── __init__.py
│   ├── main.py           # Main web routes
│   ├── api.py            # API endpoints
│   └── research.py       # Research-specific routes
├── models/               # Database models
│   ├── __init__.py
│   └── database.py       # Database operations
├── services/             # Business logic
│   ├── __init__.py
│   ├── research_service.py  # Research orchestration
│   └── settings_manager.py # Settings management
├── database/             # Database utilities
│   ├── __init__.py
│   └── schema_upgrade.py # Database migrations
├── templates/            # Jinja2 templates
│   ├── base.html
│   ├── index.html
│   ├── history.html
│   └── settings.html
└── static/              # Static assets
    ├── css/
    ├── js/
    └── images/
```

#### Configuration System (`src/kitco_research_ai/config/`)
```
config/
├── __init__.py
├── llm_config.py         # LLM provider configuration
├── env_config.py         # Environment variable handling
└── settings.py           # Application settings
```

#### Utilities (`src/kitco_research_ai/utilities/`)
```
utilities/
├── __init__.py
├── db_utils.py           # Database utilities
├── web_search.py         # Web search functionality
├── content_processor.py  # Content processing
└── report_generator.py   # Report generation
```

### Key Implementation Details

#### Database Operations
- **Connection Management**: SQLite with connection pooling
- **Schema Migrations**: Automatic schema upgrades
- **Data Integrity**: Foreign key constraints enabled
- **Backup Strategy**: File-based SQLite backups

#### Research Engine
- **Search Provider**: DuckDuckGo (no API key required)
- **Content Extraction**: BeautifulSoup for HTML parsing
- **Report Format**: Markdown with structured sections
- **Progress Tracking**: Real-time progress updates

#### LLM Integration
- **Provider Detection**: Automatic availability checking
- **Fallback Strategy**: Multiple provider support
- **Rate Limiting**: Built-in request throttling
- **Error Handling**: Graceful degradation

### Environment Configuration

#### Required Environment Variables
```bash
# OpenAI Configuration
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Custom LLM Endpoint (optional)
CUSTOM_LLM_ENDPOINT=http://localhost:1234/v1
CUSTOM_LLM_API_KEY=your_custom_api_key

# Application Configuration
SECRET_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEBUG=False
PORT=8765
```

#### Optional Environment Variables
```bash
# Database Configuration
DATABASE_PATH=data/ldr.db

# Research Configuration
MAX_SEARCH_RESULTS=10
RESEARCH_TIMEOUT=1800

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

### Performance Considerations

#### Optimization Strategies
1. **Database Indexing**: Optimized queries with proper indexes
2. **Caching**: In-memory caching for frequent operations
3. **Async Operations**: Background research processing
4. **Resource Management**: Proper cleanup of connections

#### Scalability Features
1. **Stateless Design**: Session-based state management
2. **Database Scaling**: SQLite for single-instance, PostgreSQL ready
3. **Load Balancing**: Ready for multi-instance deployment
4. **Resource Limits**: Configurable timeouts and limits

### Testing Strategy

#### Test Structure
```
tests/
├── __init__.py
├── test_web_app.py       # Web application tests
├── test_research.py      # Research engine tests
├── test_database.py      # Database operation tests
├── test_config.py        # Configuration tests
└── fixtures/             # Test data and fixtures
```

#### Test Coverage Areas
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: Component interaction testing
3. **API Tests**: Endpoint functionality testing
4. **Database Tests**: Data persistence testing

### Deployment Configurations

#### Development Environment
```bash
# Development setup
export DEBUG=True
export PORT=8765
export LOG_LEVEL=DEBUG
./start
```

#### Production Environment
```bash
# Production setup
export DEBUG=False
export PORT=80
export LOG_LEVEL=INFO
export SECRET_KEY=$(openssl rand -hex 32)
gunicorn --bind 0.0.0.0:80 app:app
```

#### Docker Deployment
```dockerfile
# Use provided Dockerfile
docker build -t kitco-research-ai .
docker run -p 8765:8765 -v $(pwd)/data:/app/data kitco-research-ai
```

### Monitoring and Maintenance

#### Health Checks
- **Application Health**: `/health` endpoint
- **Database Health**: Connection testing
- **LLM Provider Health**: Provider availability checks

#### Maintenance Tasks
1. **Database Cleanup**: Old research cleanup
2. **Log Rotation**: Log file management
3. **Dependency Updates**: Regular security updates
4. **Performance Monitoring**: Resource usage tracking

---

**This documentation serves as the definitive reference for the Kitco Research AI project structure, functionality, and operations.**
