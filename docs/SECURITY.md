# Security Configuration Guide

## Production Security Checklist

### 1. Environment Variables
Set these environment variables in production:

```bash
# Required for production
SECRET_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# API Keys (as needed)
OPENAI_API_KEY=your-openai-key
BRAVE_API_KEY=your-brave-key
SERP_API_KEY=your-serp-key
GOOGLE_PSE_API_KEY=your-google-key
GOOGLE_PSE_ENGINE_ID=your-search-engine-id

# Database (if using external DB)
DATABASE_URL=your-production-database-url
```

### 2. Content Security Policy
The current CSP policy includes `'unsafe-inline'` and `'unsafe-eval'` for development.
For production, consider tightening the CSP policy by:

1. Removing `'unsafe-inline'` and `'unsafe-eval'`
2. Using nonces for inline scripts
3. Hosting all external resources locally

### 3. HTTPS Configuration
Always use HTTPS in production:

```python
# Add to app configuration
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
```

### 4. Database Security
- Use proper database permissions
- Enable database encryption at rest
- Regular database backups
- Monitor database access logs

### 5. Rate Limiting
Consider implementing rate limiting for API endpoints:

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)
```

### 6. Input Validation
- All user inputs are validated
- SQL injection protection (using SQLAlchemy ORM)
- XSS protection (using Flask's auto-escaping)

### 7. Logging and Monitoring
- Enable security logging
- Monitor for suspicious activities
- Set up alerts for security events

### 8. Dependencies
- Regularly update dependencies
- Use `pip-audit` to check for vulnerabilities
- Pin dependency versions in production

### 9. File Upload Security
If file uploads are enabled:
- Validate file types
- Scan for malware
- Limit file sizes
- Store uploads outside web root

### 10. Error Handling
- Don't expose stack traces in production
- Log errors securely
- Use generic error messages for users
