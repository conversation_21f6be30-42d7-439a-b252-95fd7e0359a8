# Virtual Environment Migration Guide

## 🎯 Overview

The Kitco Research AI project now standardizes on using `.venv` as the virtual environment directory name, located in the project root. This guide helps you migrate from any existing virtual environment setup.

## 📍 Standard Location

**All virtual environments should be created as `.venv` in the project root directory.**

```
kitco_research_ai/
├── .venv/                    # ✅ Correct location
├── src/
├── config/
├── scripts/
└── app.py
```

## 🔄 Migration Scenarios

### **Scenario 1: You have `venv/` (old naming)**

```bash
# Automatic migration (recommended)
./scripts/update_requirements.sh

# Manual migration
mv venv .venv.backup
./scripts/setup.sh --development
```

### **Scenario 2: You have `.venv/` in wrong location**

```bash
# If .venv is in a subdirectory, remove it and recreate
rm -rf subdirectory/.venv
./scripts/setup.sh --development
```

### **Scenario 3: Multiple virtual environments**

```bash
# Clean up all virtual environments
rm -rf venv/ .venv/ ENV/ env/
./scripts/setup.sh --development
```

### **Scenario 4: Fresh installation**

```bash
# Just run setup - it will create .venv in the right place
./scripts/setup.sh --development
```

## 🛠️ How Scripts Ensure Correct Location

All project scripts now include automatic project root detection:

1. **Find project root** by looking for `app.py` and `src/kitco_research_ai/`
2. **Change to project root** before any virtual environment operations
3. **Create/use `.venv`** in the project root directory

### **Script Behavior**

```bash
# These commands work from anywhere in the project:
./scripts/setup.sh          # Creates .venv in project root
./scripts/start_app.sh       # Uses .venv from project root
./scripts/status.sh          # Checks .venv in project root

# Even if you run from subdirectories:
cd src/
../scripts/setup.sh         # Still creates .venv in project root

cd config/
../scripts/start_app.sh     # Still uses .venv from project root
```

## ✅ Verification

After migration, verify your setup:

```bash
# Check status
./scripts/status.sh

# Should show:
# ✅ Virtual environment exists (.venv/)
# ✅ Activation script found
```

## 🔧 Manual Commands

If you need to manually work with the virtual environment:

```bash
# Always run from project root
cd /path/to/kitco_research_ai

# Activate virtual environment
source .venv/bin/activate

# Deactivate
deactivate
```

## 📁 Directory Structure

**Correct structure:**
```
kitco_research_ai/
├── .venv/                    # ✅ Virtual environment here
│   ├── bin/
│   ├── lib/
│   └── pyvenv.cfg
├── src/
│   └── kitco_research_ai/
├── config/
│   ├── requirements.txt
│   ├── requirements-dev.txt
│   └── requirements-prod.txt
├── scripts/
│   ├── setup.sh
│   ├── start_app.sh
│   └── status.sh
└── app.py
```

**Incorrect structures:**
```
❌ kitco_research_ai/src/.venv/
❌ kitco_research_ai/config/.venv/
❌ kitco_research_ai/scripts/.venv/
❌ /some/other/path/.venv/
```

## 🐳 Docker Considerations

Docker containers don't need virtual environments, but for consistency:

- **Development Docker**: Mounts project root, `.venv` is ignored
- **Production Docker**: Uses system Python, no virtual environment needed

## 🔒 Security Benefits

Standardizing on `.venv` in project root:

1. **Consistent .gitignore**: All virtual environments properly ignored
2. **No accidental commits**: Virtual environment never in subdirectories that might be committed
3. **Clear separation**: Virtual environment clearly separated from source code

## 🆘 Troubleshooting

### **Problem: "Virtual environment not found"**
```bash
# Solution: Run setup from project root
cd /path/to/kitco_research_ai
./scripts/setup.sh --development
```

### **Problem: "Wrong Python version in virtual environment"**
```bash
# Solution: Recreate virtual environment
rm -rf .venv
./scripts/setup.sh --development
```

### **Problem: "Scripts can't find project root"**
```bash
# Solution: Ensure you're in the project directory
ls -la  # Should see app.py and src/ directory
./scripts/status.sh
```

### **Problem: "Permission denied"**
```bash
# Solution: Make scripts executable
chmod +x scripts/*.sh
```

## 📝 Best Practices

1. **Always use project scripts** instead of manual virtual environment commands
2. **Run scripts from any location** - they'll find the project root automatically
3. **Use `.venv` name** for consistency across all environments
4. **Keep virtual environment in project root** for easy management
5. **Use environment-specific setup** (dev/prod/standard) for appropriate dependencies

## 🔄 Automated Migration

The easiest way to migrate is using the automated script:

```bash
./scripts/update_requirements.sh
```

This script will:
- ✅ Find and backup existing virtual environments
- ✅ Create new `.venv` in project root
- ✅ Install appropriate dependencies for your chosen environment
- ✅ Test the installation
- ✅ Provide environment-specific guidance

## 📖 Related Documentation

- **[Requirements Guide](../config/README-requirements.md)** - Dependency management
- **[Setup Documentation](README.md)** - Complete setup guide
- **[Production Guide](PRODUCTION.md)** - Production deployment
- **[Requirements System Update](REQUIREMENTS_SYSTEM_UPDATE.md)** - Migration overview

---

**Need help?** Run `./scripts/help.sh` for quick reference or `./scripts/status.sh` to check your current setup.
