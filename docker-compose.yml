# ============================================================================
# Kitco Research AI - Docker Compose Configuration
# ============================================================================
# Multi-environment Docker Compose setup for development and production
# ============================================================================

version: '3.8'

services:
  # Development service
  kitco-research-ai-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: kitco-research-ai-dev
    ports:
      - "8765:8765"
    volumes:
      # Mount source code for hot-reload
      - .:/app
      # Persist data
      - ./data:/app/data
      - ./research_outputs:/app/research_outputs
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=development
      - DEBUG=True
      - PYTHONPATH=/app/src
    env_file:
      - .env
    profiles:
      - dev
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Production service
  kitco-research-ai-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kitco-research-ai-prod
    ports:
      - "8765:8765"
    volumes:
      # Only persist data, not source code
      - ./data:/app/data
      - ./research_outputs:/app/research_outputs
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
      - DEBUG=False
      - PYTHONPATH=/app/src
    env_file:
      - .env
    profiles:
      - prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching (production enhancement)
  redis:
    image: redis:7-alpine
    container_name: kitco-research-ai-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    profiles:
      - prod
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: PostgreSQL for production database
  postgres:
    image: postgres:15-alpine
    container_name: kitco-research-ai-postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=kitco_research_ai
      - POSTGRES_USER=kitco_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-changeme}
    profiles:
      - prod
      - postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U kitco_user -d kitco_research_ai"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
  postgres_data:

networks:
  default:
    name: kitco-research-ai-network
