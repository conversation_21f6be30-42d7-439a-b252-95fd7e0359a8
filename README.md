# 🔬 Kitco Research AI

AI-powered research assistant with deep, iterative analysis using LLMs and web searches.

## 🚀 Quick Start

### **Development Setup** (Recommended)
```bash
# Setup with development tools (testing, linting, debugging)
./scripts/setup.sh --development

# Start development server
./scripts/start_app.sh
```

### **Production Setup**
```bash
# Setup with minimal dependencies for production
./scripts/setup.sh --production

# Start production server
./scripts/start_app.sh
```

### **Standard Setup**
```bash
# Setup with all features (no dev tools)
./scripts/setup.sh

# Start application
./scripts/start_app.sh
```

**Access the web interface**: http://localhost:8765

## 📁 Project Structure

```
kitco_research_ai/
├── docs/                   # 📚 Documentation
├── scripts/                # 🔧 Shell scripts
├── config/                 # ⚙️ Configuration files
├── src/                    # 💻 Source code
├── data/                   # 🗄️ Application data
├── app.py                  # 🎯 Main entry point
├── setup                   # 🛠️ Setup script
├── start                   # ▶️ Start script
└── restart                 # 🔄 Restart script
```

## 📚 Documentation

- **[Complete Documentation](docs/README.md)** - Full setup and usage guide
- **[Requirements Guide](config/README-requirements.md)** - Dependency management
- **[Virtual Environment Guide](docs/VENV_MIGRATION_GUIDE.md)** - Virtual environment setup
- **[Architecture](docs/ARCHITECTURE.md)** - Technical architecture details
- **[Production Guide](docs/PRODUCTION.md)** - Production deployment
- **[Security Guide](docs/SECURITY.md)** - Security configuration

## 🛠️ Available Commands

| Command | Purpose |
|---------|---------|
| `./scripts/setup.sh [--env]` | Initial setup with environment options |
| `./scripts/start_app.sh [port]` | Start application |
| `./scripts/restart.sh [port]` | Fresh restart with cleanup |
| `./scripts/status.sh` | Check project status |
| `./scripts/help.sh` | Show help and commands |

## 🐳 Docker Support

```bash
# Development
docker-compose --profile dev up

# Production
docker-compose --profile prod up

# Custom build
docker build -f Dockerfile -t kitco-research-ai .
```

## 🔧 Configuration

1. **Environment Setup**:
   ```bash
   cp src/kitco_research_ai/defaults/.env.template .env
   ```
2. **Add API Keys**: Edit `.env` file with your OpenAI API key
3. **Choose Environment**: Use setup script with appropriate flags
4. **Start Application**: Run the start script

## 🆘 Need Help?

- Run `./scripts/help.sh` for quick reference
- Check [docs/README.md](docs/README.md) for detailed documentation
- View [troubleshooting guide](docs/README.md#-troubleshooting)

---

**Ready to start researching?** Run `./setup` then `./start` and visit http://localhost:8765! 🎉
