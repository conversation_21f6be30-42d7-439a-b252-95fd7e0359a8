{"_info": {"description": "Kitco Research AI - Default/Backup Configuration", "version": "1.0.0", "last_updated": "2025-01-01", "purpose": "DEFAULT/BACKUP configuration - DO NOT EDIT this file directly!", "note": "This file serves as the fallback when app_config.json is corrupted or missing", "usage": "Edit app_config.json for changes. Use 'python3 scripts/config_centralized.py reset' to restore from this file.", "documentation": "See docs/CENTRALIZED_CONFIGURATION.md for detailed configuration guide"}, "//": "═══════════════════════════════════════════════════════════════════════════════", "//warning1": "                           ⚠️  IMPORTANT WARNING ⚠️                        ", "//warning2": "                                                                           ", "//warning3": "  This is the DEFAULT/BACKUP configuration file. DO NOT EDIT directly!   ", "//warning4": "                                                                           ", "//warning5": "  • To change settings: Edit config/app_config.json instead              ", "//warning6": "  • To reset settings: Use 'python3 scripts/config_centralized.py reset'", "//warning7": "  • This file is used for fallback when app_config.json is corrupted    ", "//warning8": "                                                                           ", "//warning9": "═══════════════════════════════════════════════════════════════════════════════", "//structure": "This file contains the same structure as app_config.json with comprehensive", "//structure2": "comments explaining each setting. Use this as a reference for understanding", "//structure3": "what each configuration option does and what values are acceptable.", "//editorial": "═══════════════════════════════════════════════════════════════════════════════", "//editorial1": "                        EDITORIAL TEAM SETTINGS                           ", "//editorial2": "  These settings are visible and editable by the editorial team through  ", "//editorial3": "  the web interface. They control user experience and content generation. ", "//editorial4": "═══════════════════════════════════════════════════════════════════════════════", "application": {"//": "Application-level settings that control core functionality", "editorial_team_settings": {"//": "─── SEARCH & LANGUAGE PREFERENCES ───", "//note": "These settings control the look, feel, and behavior of the application interface", "enable_notifications": true, "//enable_notifications_help": "Show desktop notifications for completed research, errors, and important updates", "ui_theme": "dark", "//ui_theme_help": "Application color scheme - affects all pages and components", "ui_theme_options": ["dark", "light", "system"], "//ui_theme_options_help": "Available theme choices: dark=dark mode, light=light mode, system=follow OS setting", "search_language": "English", "//search_language_help": "Primary language for search queries and content analysis", "language_options": ["English", "French", "German", "Spanish", "Italian", "Japanese", "Chinese"], "//language_options_help": "Supported languages for search and content generation", "time_period": "y", "//time_period_help": "Default time range for search results - affects relevance and recency", "time_period_options": [{"label": "Past 24 hours", "value": "d", "//": "Most recent news and updates"}, {"label": "Past week", "value": "w", "//": "Recent developments and trends"}, {"label": "Past month", "value": "m", "//": "Current month context"}, {"label": "Past year", "value": "y", "//": "Comprehensive recent coverage"}, {"label": "All time", "value": "all", "//": "Historical and comprehensive results"}], "safe_search": true, "//safe_search_help": "Filter out explicit content from search results - recommended for professional use", "region": "us", "//region_help": "Geographic region for search results - affects local news, regulations, and market data", "region_options": [{"label": "United States", "value": "us", "//": "US-focused results, regulations, markets"}, {"label": "United Kingdom", "value": "uk", "//": "UK-focused results, regulations, markets"}, {"label": "France", "value": "fr", "//": "French-focused results, regulations, markets"}, {"label": "Germany", "value": "de", "//": "German-focused results, regulations, markets"}, {"label": "Japan", "value": "jp", "//": "Japanese-focused results, regulations, markets"}, {"label": "No Region (Worldwide)", "value": "wt-wt", "//": "Global results without regional bias"}]}, "technical_settings": {"//": "─── DATA & STORAGE SETTINGS ───", "//warning": "⚠️ These settings affect core application functionality - edit carefully!", "name": "Kitco Research AI", "//name_help": "Application name displayed in titles and branding", "version": "1.0.0", "//version_help": "Current application version - used for compatibility checks", "debug": false, "//debug_help": "Enable debug mode: detailed logging, error traces, development features. Set to false in production!", "//debug_warning": "⚠️ Never enable debug mode in production - exposes sensitive information", "enable_web": true, "//enable_web_help": "Enable web interface - disable for API-only mode", "web_interface": true, "//web_interface_help": "Enable web UI components - disable for headless operation", "host": "0.0.0.0", "//host_help": "Server bind address: 0.0.0.0=all interfaces, 127.0.0.1=localhost only", "//host_security": "⚠️ 0.0.0.0 allows external access - use 127.0.0.1 for local-only", "port": 8765, "//port_help": "Server port number - must be available and not used by other applications", "//port_range": "Valid range: 1024-65535 (avoid 1-1023 which require admin privileges)", "max_research_time_minutes": 30, "//max_research_time_minutes_help": "Maximum time allowed for any research task before timeout", "quick_research_time_minutes": 15, "//quick_research_time_minutes_help": "Time limit for quick/summary research tasks", "detailed_research_time_minutes": 45, "//detailed_research_time_minutes_help": "Time limit for comprehensive/detailed research tasks", "output_dir": "research_outputs", "//output_dir_help": "Directory where research outputs and temporary files are stored", "knowledge_accumulation": "ITERATION", "//knowledge_accumulation_help": "How knowledge is built: ITERATION=step-by-step, PARALLEL=simultaneous", "//knowledge_accumulation_options": "ITERATION, PARALLEL, HYBRID", "knowledge_accumulation_context_limit": 2000000, "//knowledge_accumulation_context_limit_help": "Maximum context size in characters for knowledge accumulation", "quick_research_stall_seconds": 180, "//quick_research_stall_seconds_help": "Timeout in seconds for quick research mode before considering it stalled (3 minutes default)", "detailed_research_stall_seconds": 420, "//detailed_research_stall_seconds_help": "Timeout in seconds for detailed research mode before considering it stalled (7 minutes default)"}}, "llm": {"editorial_team_settings": {"//": "═══ AI CONTENT GENERATION SETTINGS ═══", "//note": "These settings control how the AI generates research content and reports", "creativity_vs_determinism": 0.7, "//creativity_vs_determinism_help": "Balance between creative/varied output (1.0) and consistent/factual output (0.0). 0.7 is good for research.", "//creativity_examples": "0.0=very factual, 0.3=conservative, 0.7=balanced, 1.0=very creative", "creativity_range": [0.0, 1.0], "creativity_step": 0.05, "//creativity_range_help": "Allowed range and step size for creativity slider in UI", "output_length_tokens": 30000, "//output_length_tokens_help": "Maximum length of generated content in tokens (roughly 4 characters per token)", "//output_length_examples": "1000=short summary, 5000=detailed report, 30000=comprehensive analysis", "output_length_range": [100, 4096], "output_length_step": 100, "//output_length_range_help": "Allowed range and step size for output length slider in UI"}, "technical_settings": {"//": "─── API ENDPOINTS & KEYS ───", "//warning": "⚠️ Incorrect LLM settings can cause generation failures or poor quality output", "provider": "openai", "//provider_help": "AI service provider - determines which API is used for content generation", "provider_options": ["openai", "anthropic", "ollama", "lmstudio", "vllm", "openai_endpoint"], "//provider_descriptions": {"openai": "OpenAI GPT models (requires API key)", "anthropic": "Anthropic Claude models (requires API key)", "ollama": "Local Ollama server (free, requires local setup)", "lmstudio": "Local LM Studio server (free, requires local setup)", "vllm": "vLLM server for high-performance inference", "openai_endpoint": "Custom OpenAI-compatible endpoint"}, "model": "gpt-4.1-mini", "//model_help": "Specific AI model to use - only GPT-4.1 models available", "model_options": ["gpt-4.1-mini", "gpt-4.1-nano"], "//model_performance": {"gpt-4.1-mini": "Fast and efficient - 2x faster than GPT-4o with same reasoning", "gpt-4.1-nano": "Ultra-efficient for simple tasks - most cost-effective option"}, "temperature": 0.7, "//temperature_help": "Randomness in AI output: 0.0=deterministic, 1.0=creative. 0.7 is balanced for research.", "max_tokens": 30000, "//max_tokens_help": "Maximum tokens the AI can generate in one response", "context_window_size": 128000, "//context_window_size_help": "Maximum context size the model can process (varies by model)", "supports_max_tokens": true, "//supports_max_tokens_help": "Whether this model supports max_tokens parameter", "//security_warning": "⚠️ API keys are sensitive - never share or commit to version control", "lmstudio_url": "http://localhost:1234", "//lmstudio_url_help": "URL for local LM Studio server", "openai_endpoint_url": "https://openrouter.ai/api/v1", "//openai_endpoint_url_help": "Custom OpenAI-compatible API endpoint", "ollama_url": "http://localhost:11434", "//ollama_url_help": "URL for local Ollama server", "openai_api_key": "OPENAI_API_KEY", "//openai_api_key_help": "OpenAI API key - replace with actual key or set via environment variable", "//openai_api_key_env": "Can be set via OPENAI_API_KEY environment variable", "//openai_api_key_format": "New format: sk-proj-... (164 chars) | Legacy format: sk-... (51 chars)", "//openai_api_key_security": "⚠️ Keep secure! Never commit to version control or share publicly", "anthropic_api_key": "ANTHROPIC_API_KEY", "//anthropic_api_key_help": "Anthropic API key - replace with actual key or set via environment variable", "//anthropic_api_key_env": "Can be set via ANTHROPIC_API_KEY environment variable", "openai_endpoint_api_key": "OPENAI_ENDPOINT_API_KEY", "//openai_endpoint_api_key_help": "API key for custom OpenAI-compatible endpoint"}}, "search": {"//": "Search engine configuration for web research", "editorial_team_settings": {"//": "═══ SEARCH QUALITY & FILTERING SETTINGS ═══", "//note": "These settings control how web searches are performed and filtered", "quality_check_urls": true, "//quality_check_urls_help": "Verify that URLs are accessible and contain relevant content before including in research"}, "technical_settings": {"//": "─── SEARCH QUALITY & FILTERING ───", "//note": "These settings control search performance, accuracy, and resource usage", "tool": "searxng", "//tool_help": "Primary search engine/tool to use for web research", "tool_options": ["auto", "serp<PERSON>i", "searxng", "google_pse", "duckduck<PERSON>", "brave"], "//tool_descriptions": {"auto": "Automatically select best engine based on query", "serpapi": "SerpAPI service (requires API key, high quality)", "searxng": "Self-hosted SearXNG (free, requires setup)", "google_pse": "Google Programmable Search (requires API key)", "duckduckgo": "DuckDuckGo API (free, privacy-focused)", "brave": "Brave Search API (free tier available)"}, "default_search_engine": "searxng", "//default_search_engine_help": "Fallback search engine if primary tool fails", "iterations": 2, "//iterations_help": "Number of search iterations per research task (more = thorough, slower)", "max_results": 50, "//max_results_help": "Maximum search results to retrieve per query", "max_filtered_results": 20, "//max_filtered_results_help": "Maximum results after relevance filtering", "final_max_results": 100, "//final_max_results_help": "Maximum total results across all searches for one research task", "questions_per_iteration": 2, "//questions_per_iteration_help": "Number of different search queries per iteration", "searches_per_section": 2, "//searches_per_section_help": "Number of searches performed for each report section", "skip_relevance_filter": false, "//skip_relevance_filter_help": "Skip AI-based relevance filtering (faster but may include irrelevant results)", "snippets_only": true, "//snippets_only_help": "Use only search result snippets instead of full page content (faster, less comprehensive)", "timeout": 30, "//timeout_help": "Timeout in seconds for individual search requests"}}, "reports": {"//": "─── EXPORT & SHARING OPTIONS ───", "//note": "All report settings are visible to editorial team as they directly affect content quality and output", "detailed_citations": true, "//detailed_citations_help": "Include full source URLs, publication dates, and author information in reports", "enable_fact_checking": true, "//enable_fact_checking_help": "Cross-reference facts across multiple sources and flag potential inconsistencies", "searches_per_section": 2, "//searches_per_section_help": "Number of search queries performed for each section of the report (more = comprehensive, slower)", "default_format": "markdown", "//default_format_help": "Default output format for generated reports", "format_options": ["markdown", "html", "pdf"], "//format_options_help": "Available export formats: markdown=text with formatting, html=web page, pdf=printable document", "include_sources": true, "//include_sources_help": "Add source list and bibliography to end of reports", "include_timestamps": true, "//include_timestamps_help": "Add generation date/time and source publication dates to reports", "max_report_length": 5000, "//max_report_length_help": "Maximum word count for generated reports (prevents overly long outputs)", "auto_save": true, "//auto_save_help": "Automatically save reports to disk when generation completes", "output_directory": "reports", "//output_directory_help": "Folder where completed reports are saved", "quality_settings": {"fact_checking": true, "//fact_checking_help": "Verify facts against multiple sources", "source_verification": true, "//source_verification_help": "Check that sources are credible and accessible", "bias_detection": true, "//bias_detection_help": "Analyze content for potential bias and flag concerns", "readability_score": true, "//readability_score_help": "Calculate and optimize text readability for target audience"}, "export_options": {"enable_pdf": true, "//enable_pdf_help": "Allow exporting reports as PDF documents", "enable_word": true, "//enable_word_help": "Allow exporting reports as Microsoft Word documents", "enable_email": false, "//enable_email_help": "Allow emailing reports directly from the application", "watermark": "Kitco Research AI", "//watermark_help": "Text watermark added to exported documents for branding"}}, "//dev": "═══════════════════════════════════════════════════════════════════════════════", "//dev_comment1": "                      DEVELOPER/TECHNICAL SETTINGS                        ", "//dev_comment2": "  These settings are HIDDEN from editorial team and control technical   ", "//dev_comment3": "  aspects like servers, databases, APIs, and system configuration.      ", "//dev_comment4": "  ⚠️  CAUTION: Incorrect values can break the application!              ", "//dev_comment5": "═══════════════════════════════════════════════════════════════════════════════", "search_engines": {"//": "═══ INDIVIDUAL SEARCH ENGINE CONFIGURATIONS ═══", "//note": "Detailed configuration for each supported search engine", "auto": {"display_name": "Auto", "description": "Attempt to choose the best combination of search engines automatically", "class_name": "MetaSearchEngine", "module_path": "kitco_research_ai.web_search_engines.engines.meta_search_engine", "reliability": 0.85, "requires_api_key": false, "requires_llm": true, "max_engines_to_try": 3, "use_api_key_services": true, "strengths": ["intelligent engine selection", "adaptable to query type", "fallback capabilities"], "weaknesses": ["slightly slower due to LLM analysis"]}, "searxng": {"display_name": "SearXNG (Locally-hosted)", "description": "A locally-hosted meta-search engine", "class_name": "SearXNGSearchEngine", "module_path": "kitco_research_ai.web_search_engines.engines.search_engine_searxng", "reliability": 1.0, "requires_api_key": false, "supports_full_search": true, "default_params": {"instance_url": "http://localhost:8080", "categories": ["general"], "delay_between_requests": 0, "include_full_content": true, "language": "en", "max_results": 15, "safe_search": "OFF"}, "strengths": ["comprehensive general information", "current events and news", "technical documentation"], "weaknesses": ["requires self-hosting", "depends on other search engines"]}, "duckduckgo": {"display_name": "DuckDuckGo", "description": "Privacy-focused search engine", "class_name": "DuckDuckGoSearchEngine", "module_path": "kitco_research_ai.web_search_engines.engines.search_engine_duckduckgo", "reliability": 0.8, "requires_api_key": false, "supports_full_search": true, "default_params": {"region": "us-en", "safe_search": "moderate", "time_period": "y"}, "strengths": ["privacy-focused", "no tracking", "instant answers"], "weaknesses": ["smaller index", "less comprehensive than Google"]}}, "security": {"//": "─── ENCRYPTION & SECRETS ───", "//critical": "🔒 CRITICAL: These settings control application security - handle with extreme care!", "session_timeout_minutes": 60, "//session_timeout_minutes_help": "Minutes before user sessions expire and require re-login", "//session_timeout_security": "Shorter timeouts = more secure, longer = more convenient", "max_login_attempts": 5, "//max_login_attempts_help": "Maximum failed login attempts before account lockout", "//max_login_attempts_security": "Lower values prevent brute force attacks", "require_https": false, "//require_https_help": "Force HTTPS connections - MUST be true in production!", "//require_https_warning": "⚠️ Set to true in production to encrypt all communications", "api_rate_limit": 100, "//api_rate_limit_help": "Maximum API requests per minute per user/IP", "//api_rate_limit_security": "Prevents abuse and DoS attacks", "allowed_origins": ["localhost", "127.0.0.1"], "//allowed_origins_help": "Domains/IPs allowed to access the application", "//allowed_origins_security": "Restricts CORS access - add production domains here", "secret_key": "change-this-in-production", "//secret_key_help": "Secret key for session encryption and security tokens", "//secret_key_critical": "🔒 MUST be changed in production! Use long, random string!", "//secret_key_generation": "Generate with: python -c 'import secrets; print(secrets.token_urlsafe(32))'", "data_protection": {"//": "Data protection and privacy settings", "encrypt_api_keys": true, "//encrypt_api_keys_help": "Encrypt API keys in database storage", "log_sensitive_data": false, "//log_sensitive_data_help": "Whether to log sensitive information (API keys, tokens) - should be false!", "//log_sensitive_data_warning": "⚠️ Never enable in production - exposes secrets in logs", "auto_backup": true, "//auto_backup_help": "Automatically backup configuration and data", "backup_retention_days": 30, "//backup_retention_days_help": "How long to keep backup files before deletion"}}, "database": {"//": "═══ DATABASE CONFIGURATION ═══", "//note": "Database settings for storing application data, settings, and research history", "path": "data/ldr.db", "//path_help": "SQLite database file location - ensure directory exists and is writable", "//path_backup": "Database is automatically backed up - see backup_path setting", "backup_path": "backups/database", "//backup_path_help": "Directory where database backups are stored", "auto_backup_hours": 24, "//auto_backup_hours_help": "Hours between automatic database backups (24 = daily)", "max_connections": 10, "//max_connections_help": "Maximum concurrent database connections", "//max_connections_performance": "Higher values allow more concurrent users but use more memory", "query_timeout_seconds": 30, "//query_timeout_seconds_help": "Timeout for database queries before cancellation"}, "logging": {"//": "─── COMPONENT-SPECIF<PERSON> LOG LEVELS ───", "//note": "Set different log levels for different parts of the application", "level": "INFO", "//level_help": "Minimum log level to record - DEBUG=everything, INFO=normal, WARNING=problems only, ERROR=serious issues only", "level_options": ["DEBUG", "INFO", "WARNING", "ERROR"], "//level_performance": "DEBUG level can impact performance and create large log files", "file_path": "logs/app.log", "//file_path_help": "Log file location - ensure directory exists and is writable", "max_file_size_mb": 100, "//max_file_size_mb_help": "Maximum size of log file before rotation (in megabytes)", "backup_count": 5, "//backup_count_help": "Number of rotated log files to keep (app.log.1, app.log.2, etc.)", "format": "{time} | {level} | {message}", "//format_help": "Log message format - includes timestamp, level, and message", "categories": {"database": "INFO", "//database_help": "Database operations and queries", "api": "INFO", "//api_help": "API requests and responses", "search": "INFO", "//search_help": "Search engine operations and results", "llm": "INFO", "//llm_help": "AI/LLM generation and API calls", "security": "WARNING", "//security_help": "Security events, authentication, authorization"}}, "features": {"//": "═══ FEATURE FLAGS & EXPERIMENTAL SETTINGS ═══", "//note": "Enable/disable application features - useful for testing and gradual rollouts", "experimental_ai": false, "//experimental_ai_help": "Enable experimental AI features (may be unstable)", "//experimental_ai_warning": "⚠️ Experimental features may cause unexpected behavior", "advanced_search": true, "//advanced_search_help": "Enable advanced search options and filters in UI", "real_time_updates": false, "//real_time_updates_help": "Enable real-time updates via WebSocket connections", "//real_time_updates_performance": "May increase server load and memory usage", "beta_features": false, "//beta_features_help": "Enable beta features for testing (may have bugs)", "maintenance_mode": false, "//maintenance_mode_help": "Enable maintenance mode - blocks normal user access", "//maintenance_mode_usage": "Use during updates, backups, or system maintenance"}, "ui_customization": {"//": "═══ USER INTERFACE CUSTOMIZATION ═══", "//note": "Settings that control the appearance and branding of the application", "editorial_visible": {"//": "UI settings visible to editorial team", "company_logo": "assets/logo.png", "//company_logo_help": "Path to company logo image file (PNG, JPG, SVG supported)", "company_name": "Kitco Research", "//company_name_help": "Company name displayed in header and titles", "primary_color": "#1f2937", "//primary_color_help": "Primary brand color (hex code) - used for headers, buttons, links", "accent_color": "#3b82f6", "//accent_color_help": "Accent color (hex code) - used for highlights and interactive elements"}, "developer_only": {"//": "UI settings only accessible to developers/administrators", "show_debug_info": false, "//show_debug_info_help": "Show debug information in UI (performance metrics, system info)", "//show_debug_info_warning": "⚠️ Only enable for debugging - may confuse users", "enable_dev_tools": false, "//enable_dev_tools_help": "Enable developer tools and admin panels in UI", "custom_css_path": "", "//custom_css_path_help": "Path to custom CSS file for additional styling", "analytics_tracking": false, "//analytics_tracking_help": "Enable user analytics and usage tracking", "//analytics_privacy": "Consider privacy implications before enabling"}}}