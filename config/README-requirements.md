# Requirements Files Guide

This directory contains multiple requirements files for different use cases and environments.

## 📁 Files Overview

### `requirements.txt` - **Main Requirements (Comprehensive)**
- **Purpose**: Complete dependency list with detailed categorization and comments
- **Use Case**: Development, testing, and production (includes everything)
- **Features**: 
  - Organized by functional categories
  - Detailed comments explaining each package's purpose
  - All dependencies needed for full functionality

### `requirements-prod.txt` - **Production Requirements (Minimal)**
- **Purpose**: Minimal production dependencies only
- **Use Case**: Production deployments where you want to minimize attack surface
- **Features**:
  - Excludes development and testing tools
  - Excludes debugging and profiling tools
  - Optimized for security and performance

### `requirements-dev.txt` - **Development Requirements (Extended)**
- **Purpose**: All dependencies including development tools
- **Use Case**: Local development, testing, and code quality
- **Features**:
  - Includes all production requirements
  - Adds testing frameworks (pytest, coverage)
  - Adds code quality tools (black, mypy, pylint)
  - Adds documentation tools (sphinx)
  - Adds debugging and profiling tools

## 🚀 Installation Instructions

### For Development (Recommended for local work)
```bash
# Create virtual environment in project root
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install development dependencies (includes everything)
pip install -r config/requirements-dev.txt
```

**Note**: The virtual environment (`.venv`) is always created in the project root directory, regardless of where you run the commands from.

### For Production Deployment
```bash
# Create virtual environment in project root
python -m venv .venv
source .venv/bin/activate

# Install minimal production dependencies
pip install -r config/requirements-prod.txt
```

### For Standard Installation
```bash
# Create virtual environment in project root
python -m venv .venv
source .venv/bin/activate

# Install main requirements (comprehensive but without dev tools)
pip install -r config/requirements.txt
```

## 🔧 Maintenance

### Adding New Dependencies

1. **For Production Dependencies**:
   ```bash
   pip install new-package
   # Add to requirements.txt with proper category and comment
   # Add to requirements-prod.txt if needed in production
   ```

2. **For Development Dependencies**:
   ```bash
   pip install new-dev-package
   # Add to requirements-dev.txt with proper category and comment
   ```

### Updating Dependencies
```bash
# Check for outdated packages
pip list --outdated

# Update specific package
pip install --upgrade package-name

# Update requirements file
pip freeze > temp-requirements.txt
# Then manually merge changes into appropriate requirements file
```

### Security Auditing
```bash
# Install security audit tool (included in requirements-dev.txt)
pip install pip-audit

# Run security audit
pip-audit

# Check for known vulnerabilities
safety check
```

## 📊 Dependency Categories

The main `requirements.txt` file organizes dependencies into these categories:

- **Core Web Framework & Server**: Flask, SocketIO, etc.
- **AI & Machine Learning**: LangChain, OpenAI, PyTorch, etc.
- **Database & Data Persistence**: SQLAlchemy, Alembic
- **Web Scraping & Search**: Playwright, BeautifulSoup, search APIs
- **HTTP Clients & Networking**: Requests, httpx, aiohttp
- **Document Processing**: PDF tools, unstructured data processing
- **Configuration & Environment**: Pydantic, python-dotenv
- **Logging & Monitoring**: Loguru, colorlog
- **Async & Concurrency**: anyio, nest-asyncio
- **Utilities & Helpers**: Various utility libraries
- **Testing & Development**: pytest, code quality tools (dev only)

## 🔒 Security Considerations

### Production Security
- Use `requirements-prod.txt` for production to minimize attack surface
- Regularly audit dependencies with `pip-audit` and `safety`
- Pin exact versions to ensure reproducible builds
- Consider using `pip-tools` for dependency resolution

### Development Security
- Keep development tools separate from production
- Use virtual environments to isolate dependencies
- Regularly update dependencies to get security patches

## 🐳 Docker Usage

### Production Dockerfile
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY config/requirements-prod.txt .
RUN pip install --no-cache-dir -r requirements-prod.txt
COPY . .
EXPOSE 8765
CMD ["python", "app.py"]
```

### Development Dockerfile
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY config/requirements-dev.txt .
RUN pip install --no-cache-dir -r requirements-dev.txt
COPY . .
EXPOSE 8765
CMD ["python", "app.py", "--debug"]
```

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Install dependencies
  run: |
    python -m pip install --upgrade pip
    pip install -r config/requirements-dev.txt  # For testing
    
- name: Run tests
  run: pytest

- name: Security audit
  run: pip-audit
```

## 📝 Notes

- **Playwright**: Requires additional browser installation: `playwright install`
- **NLTK**: May require downloading additional data: `python -c "import nltk; nltk.download('punkt')"`
- **PyTorch**: CPU version included; for GPU support, see PyTorch installation guide
- **Database**: SQLite is used by default; for production, consider PostgreSQL

## 🆘 Troubleshooting

### Common Issues

1. **Playwright browser installation**:
   ```bash
   playwright install
   ```

2. **NLTK data missing**:
   ```bash
   python -c "import nltk; nltk.download('all')"
   ```

3. **Permission errors on Windows**:
   - Run terminal as administrator
   - Or use `--user` flag: `pip install --user -r requirements.txt`

4. **Memory issues with large ML models**:
   - Consider using CPU-only versions
   - Increase virtual memory/swap space
   - Use model quantization techniques

For more help, see the main project documentation in `docs/`.
