"""
Optimized Centralized Configuration System
==========================================

High-performance configuration management with lazy loading and caching.
Includes secure secrets management and environment variable integration.
"""

import json
import os
import shutil
from functools import lru_cache
from pathlib import Path
from typing import Any, Dict, List, Optional
from loguru import logger

# Import secure modules
try:
    from .secrets_manager import SecureSecretsManager, get_secret
    from .environment import EnvironmentLoader, env_loader
    SECURITY_AVAILABLE = True
except ImportError:
    logger.warning("Security modules not available - using basic configuration only")
    SECURITY_AVAILABLE = False


class OptimizedCentralizedConfig:
    """Optimized centralized configuration manager with secure secrets support"""

    def __init__(self,
                 current_config_file: str = "config/app_config.json",
                 default_config_file: str = "config/default_config.json"):
        self.current_config_file = Path(current_config_file)
        self.default_config_file = Path(default_config_file)
        self._config = None  # Lazy loading
        self._defaults = None  # Lazy loading
        self._config_mtime = 0
        self._defaults_mtime = 0
        self._validation_cache = None

        # Initialize security components if available
        if SECURITY_AVAILABLE:
            self.secrets_manager = SecureSecretsManager()
            self.env_loader = env_loader
        else:
            self.secrets_manager = None
            self.env_loader = None
        
    @property
    def config(self) -> Dict[str, Any]:
        """Lazy-loaded current configuration with file change detection"""
        current_mtime = self.current_config_file.stat().st_mtime if self.current_config_file.exists() else 0
        
        if self._config is None or current_mtime > self._config_mtime:
            self._load_current_config()
            self._config_mtime = current_mtime
            self._validation_cache = None  # Invalidate validation cache
            
        return self._config
    
    @property 
    def defaults(self) -> Dict[str, Any]:
        """Lazy-loaded default configuration with file change detection"""
        defaults_mtime = self.default_config_file.stat().st_mtime if self.default_config_file.exists() else 0
        
        if self._defaults is None or defaults_mtime > self._defaults_mtime:
            self._load_defaults()
            self._defaults_mtime = defaults_mtime
            
        return self._defaults
    
    def _load_current_config(self):
        """Load current configuration with fallback"""
        try:
            if self.current_config_file.exists():
                with open(self.current_config_file, 'r') as f:
                    self._config = json.load(f)
                logger.debug(f"Current configuration loaded from {self.current_config_file}")
            else:
                logger.warning(f"Current config not found: {self.current_config_file}")
                self._create_current_from_defaults()
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in current config: {e}")
            logger.info("Falling back to defaults...")
            self._create_current_from_defaults()
        except Exception as e:
            logger.error(f"Error loading current config: {e}")
            self._create_current_from_defaults()
    
    def _load_defaults(self):
        """Load default configuration"""
        try:
            if not self.default_config_file.exists():
                raise FileNotFoundError(f"Default configuration file not found: {self.default_config_file}")
            
            with open(self.default_config_file, 'r') as f:
                self._defaults = json.load(f)
            
            logger.debug(f"Default configuration loaded from {self.default_config_file}")
            
        except Exception as e:
            logger.error(f"Error loading default configuration: {e}")
            raise
    
    def _create_current_from_defaults(self):
        """Create current config from defaults"""
        try:
            self._config = self.defaults.copy()
            self._save_current_config()
            logger.info("Current configuration created from defaults")
        except Exception as e:
            logger.error(f"Failed to create current config from defaults: {e}")
            raise
    
    def _save_current_config(self):
        """Save current configuration to file"""
        try:
            self.current_config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.current_config_file, 'w') as f:
                json.dump(self._config, f, indent=2)
            
            logger.debug(f"Current configuration saved to {self.current_config_file}")
            
        except Exception as e:
            logger.error(f"Error saving current configuration: {e}")
            raise
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value with optimized access and secure secret resolution

        Args:
            key: Dot-separated configuration key
            default: Default value if not found

        Returns:
            Configuration value or default
        """
        # Use simple caching for string keys only
        if isinstance(default, (str, int, float, bool, type(None))):
            cache_key = f"{key}:{default}"
            if hasattr(self, '_simple_cache') and cache_key in self._simple_cache:
                return self._simple_cache[cache_key]

        try:
            keys = key.split('.')
            value = self.config

            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    # Try environment variable override if security is available
                    if SECURITY_AVAILABLE and self.env_loader:
                        env_value = self._try_environment_override(key, default)
                        if env_value is not None:
                            return env_value
                    return default

            # Resolve secrets if value looks like an environment variable reference
            if isinstance(value, str) and SECURITY_AVAILABLE:
                resolved_value = self._resolve_secret_value(value, key)
                if resolved_value != value:
                    value = resolved_value

            # Cache simple values only (but not secrets)
            if isinstance(default, (str, int, float, bool, type(None))) and not self._is_secret_key(key):
                if not hasattr(self, '_simple_cache'):
                    self._simple_cache = {}
                if len(self._simple_cache) < 1000:  # Limit cache size
                    self._simple_cache[cache_key] = value

            return value

        except Exception as e:
            logger.debug(f"Error getting config key '{key}': {e}")
            return default
    
    def _try_environment_override(self, key: str, default: Any) -> Any:
        """Try to get value from environment variables."""
        if not self.env_loader:
            return None

        # Convert config key to environment variable name
        env_key = key.replace('.', '_').upper()

        # Try different environment variable patterns
        env_patterns = [
            env_key,
            f"KITCO_{env_key}",
            f"KRA_{env_key}",
        ]

        for pattern in env_patterns:
            value = os.getenv(pattern)
            if value is not None:
                logger.debug(f"Using environment override {pattern} for {key}")
                return value

        return None

    def _resolve_secret_value(self, value: str, key: str) -> str:
        """Resolve secret values from environment variables or encrypted storage."""
        if not self.secrets_manager:
            return value

        # Check if value looks like an environment variable reference
        if isinstance(value, str) and value.isupper() and '_' in value:
            # Try to get from secrets manager
            secret_value = self.secrets_manager.get_secret(value)
            if secret_value and secret_value != value:
                logger.debug(f"Resolved secret for {key}")
                return secret_value

        return value

    def _is_secret_key(self, key: str) -> bool:
        """Check if a configuration key contains sensitive data."""
        secret_indicators = [
            'api_key', 'secret', 'password', 'token', 'credential',
            'private_key', 'cert', 'auth', 'encryption_key'
        ]
        key_lower = key.lower()
        return any(indicator in key_lower for indicator in secret_indicators)

    def has(self, key: str) -> bool:
        """Check if configuration key exists"""
        return self.get(key, "__NOT_FOUND__") != "__NOT_FOUND__"

    def get_section(self, section: str) -> Dict[str, Any]:
        """Get entire configuration section"""
        return self.get(section, {})
    
    def reload(self):
        """Force reload configuration and clear caches"""
        logger.info("Reloading configuration...")
        self._config = None
        self._defaults = None
        self._config_mtime = 0
        self._defaults_mtime = 0
        self._validation_cache = None
        if hasattr(self, '_simple_cache'):
            self._simple_cache.clear()  # Clear simple cache
    
    def reset_to_defaults(self):
        """Reset current configuration to defaults"""
        logger.info("Resetting configuration to defaults...")
        try:
            self._config = self.defaults.copy()
            self._save_current_config()
            self._config_mtime = self.current_config_file.stat().st_mtime
            self._validation_cache = None
            if hasattr(self, '_simple_cache'):
                self._simple_cache.clear()
            logger.info("Configuration successfully reset to defaults")
        except Exception as e:
            logger.error(f"Error resetting to defaults: {e}")
            raise
    
    def backup_current_config(self, backup_path: Optional[str] = None):
        """Create backup of current configuration"""
        if backup_path is None:
            backup_path = f"{self.current_config_file}.backup"
        
        try:
            shutil.copy2(self.current_config_file, backup_path)
            logger.info(f"Configuration backed up to {backup_path}")
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            raise
    
    def validate(self) -> List[str]:
        """Validate configuration with caching and security checks"""
        if self._validation_cache is not None:
            return self._validation_cache

        issues = []
        config_data = self.config

        # Quick validation checks
        try:
            # Check required sections
            required_sections = ['application', 'llm', 'search', 'reports']
            for section in required_sections:
                if section not in config_data:
                    issues.append(f"Missing required section: {section}")

            # Check port validity
            port = self.get('application.technical_settings.port')
            if not isinstance(port, int) or port < 1 or port > 65535:
                issues.append(f"Invalid port number: {port}")

            # Security validation if available
            if SECURITY_AVAILABLE and self.secrets_manager:
                security_issues = self.secrets_manager.validate_secrets()
                issues.extend(security_issues)

                # Check for insecure defaults in production
                if self.secrets_manager.is_production:
                    secret_key = self.get('security.secret_key', '')
                    if 'change-this' in secret_key.lower() or 'development' in secret_key.lower():
                        issues.append("Insecure SECRET_KEY detected in production")

            # Check API keys in production
            if not self.get('application.technical_settings.debug', False):
                api_keys = [
                    'llm.technical_settings.openai_api_key',
                    'llm.technical_settings.anthropic_api_key'
                ]
                for key_path in api_keys:
                    key_value = self.get(key_path, '')
                    if not key_value or key_value in ['OPENAI_API_KEY', 'ANTHROPIC_API_KEY']:
                        issues.append("API key is empty in production mode")
                        break

            # Environment validation if available
            if SECURITY_AVAILABLE and self.env_loader:
                env_issues = self.env_loader.get_validation_errors()
                issues.extend(env_issues)

        except Exception as e:
            issues.append(f"Validation error: {e}")

        self._validation_cache = issues
        return issues
    
    @lru_cache(maxsize=10)
    def get_editorial_settings(self) -> Dict[str, Any]:
        """Get editorial team settings with caching"""
        editorial_settings = {}
        
        # Application editorial settings
        app_editorial = self.get('application.editorial_team_settings', {})
        if app_editorial:
            editorial_settings['application'] = app_editorial
        
        # Search editorial settings  
        search_editorial = self.get('search.editorial_team_settings', {})
        if search_editorial:
            editorial_settings['search'] = search_editorial
        
        # LLM editorial settings
        llm_editorial = self.get('llm.editorial_team_settings', {})
        if llm_editorial:
            editorial_settings['llm'] = llm_editorial
        
        # Reports settings (all visible to editorial team)
        reports = self.get('reports', {})
        if reports:
            editorial_settings['reports'] = reports
        
        # UI customization editorial settings
        ui_editorial = self.get('ui_customization.editorial_visible', {})
        if ui_editorial:
            editorial_settings['ui_customization'] = ui_editorial
        
        return editorial_settings
    
    @lru_cache(maxsize=10)
    def get_technical_settings(self) -> Dict[str, Any]:
        """Get technical settings with caching"""
        technical_settings = {}
        
        # Application technical settings
        app_technical = self.get('application.technical_settings', {})
        if app_technical:
            technical_settings['application'] = app_technical
        
        # LLM technical settings
        llm_technical = self.get('llm.technical_settings', {})
        if llm_technical:
            technical_settings['llm'] = llm_technical
        
        # Search technical settings
        search_technical = self.get('search.technical_settings', {})
        if search_technical:
            technical_settings['search'] = search_technical
        
        # Always technical sections
        for section in ['security', 'database', 'logging', 'search_engines']:
            section_data = self.get(section, {})
            if section_data:
                technical_settings[section] = section_data
        
        # Developer-only UI customization
        ui_developer = self.get('ui_customization.developer_only', {})
        if ui_developer:
            technical_settings['ui_customization'] = ui_developer
        
        return technical_settings
    
    @lru_cache(maxsize=5)
    def get_safe_summary(self) -> Dict[str, Any]:
        """Get safe configuration summary with caching"""
        return {
            'application': {
                'name': self.get('application.technical_settings.name'),
                'version': self.get('application.technical_settings.version'),
                'debug': self.get('application.technical_settings.debug'),
                'theme': self.get('application.editorial_team_settings.ui_theme'),
            },
            'search': {
                'tool': self.get('search.technical_settings.tool'),
                'max_results': self.get('search.technical_settings.max_results'),
                'language': self.get('application.editorial_team_settings.search_language'),
            },
            'llm': {
                'provider': self.get('llm.technical_settings.provider'),
                'model': self.get('llm.technical_settings.model'),
                'creativity': self.get('llm.editorial_team_settings.creativity_vs_determinism'),
                'has_api_key': bool(self.get('llm.technical_settings.openai_api_key')),
            },
            'features': self.get('features', {}),
        }


# Global optimized configuration instance
config = OptimizedCentralizedConfig()


# Optimized convenience functions with caching
@lru_cache(maxsize=1)
def get_app_port() -> int:
    """Get application port with caching"""
    return config.get('application.technical_settings.port', 8765)


@lru_cache(maxsize=1)
def get_app_host() -> str:
    """Get application host with caching"""
    return config.get('application.technical_settings.host', '0.0.0.0')


@lru_cache(maxsize=1)
def is_debug_mode() -> bool:
    """Check if debug mode is enabled with caching"""
    return config.get('application.technical_settings.debug', False)


@lru_cache(maxsize=1)
def get_llm_provider() -> str:
    """Get LLM provider with caching"""
    return config.get('llm.technical_settings.provider', 'openai')


@lru_cache(maxsize=1)
def get_llm_model() -> str:
    """Get LLM model with caching"""
    return config.get('llm.technical_settings.model', 'gpt-4.1-mini')


def is_feature_enabled(feature_name: str) -> bool:
    """Check if feature is enabled"""
    return config.get(f'features.{feature_name}', False)


def reload_config():
    """Reload configuration and clear all caches"""
    config.reload()
    # Clear function caches
    get_app_port.cache_clear()
    get_app_host.cache_clear()
    is_debug_mode.cache_clear()
    get_llm_provider.cache_clear()
    get_llm_model.cache_clear()


def validate_config() -> List[str]:
    """Validate configuration"""
    return config.validate()


# Secure secret access functions
def get_secret_safe(key: str, default: Optional[str] = None) -> Optional[str]:
    """Get a secret value safely through the configuration system."""
    if SECURITY_AVAILABLE:
        return get_secret(key, default)
    else:
        # Fallback to environment variable
        return os.getenv(key, default)


def get_api_key(provider: str) -> Optional[str]:
    """Get API key for a specific provider."""
    key_mapping = {
        'openai': 'OPENAI_API_KEY',
        'anthropic': 'ANTHROPIC_API_KEY',
        'brave': 'BRAVE_API_KEY',
        'serpapi': 'SERPAPI_API_KEY',
        'google_pse': 'GOOGLE_PSE_API_KEY',
    }

    env_key = key_mapping.get(provider.lower())
    if env_key:
        return get_secret_safe(env_key)
    return None


def is_production_mode() -> bool:
    """Check if running in production mode."""
    if SECURITY_AVAILABLE and config.secrets_manager:
        return config.secrets_manager.is_production
    return os.getenv('ENVIRONMENT', 'development') == 'production'


def get_security_summary() -> Dict[str, Any]:
    """Get a summary of security status."""
    summary = {
        'security_available': SECURITY_AVAILABLE,
        'production_mode': is_production_mode(),
        'debug_mode': is_debug_mode(),
    }

    if SECURITY_AVAILABLE and config.secrets_manager:
        summary.update(config.secrets_manager.get_safe_summary())

    return summary


# Optional: Auto-validate only in debug mode to improve startup performance
if __name__ != "__main__":
    debug_mode = os.getenv('DEBUG', '').lower() in ('1', 'true', 'yes')
    if debug_mode:
        issues = validate_config()
        if issues:
            logger.warning("Configuration validation issues found:")
            for issue in issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("Configuration validation passed")
