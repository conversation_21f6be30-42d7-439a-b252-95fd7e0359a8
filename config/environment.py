#!/usr/bin/env python3
"""
Environment Variable Loader for Kitco Research AI

This module provides secure loading and management of environment variables
with support for .env files, type conversion, validation, and secure defaults.

Features:
- Automatic .env file loading
- Type-safe environment variable access
- Validation and sanitization
- Secure default values
- Development vs production handling
- Environment variable documentation
"""

import os
import logging
from typing import Any, Dict, List, Optional, Union, Callable, Type
from pathlib import Path
import json

logger = logging.getLogger(__name__)


class EnvironmentLoader:
    """
    Secure environment variable loader with validation and type conversion.
    """
    
    def __init__(self, project_root: Optional[str] = None, auto_load: bool = True):
        """
        Initialize the environment loader.
        
        Args:
            project_root: Root directory of the project
            auto_load: Whether to automatically load .env files
        """
        self.project_root = Path(project_root) if project_root else self._find_project_root()
        self.env_files = [
            self.project_root / '.env.local',  # Local overrides (highest priority)
            self.project_root / '.env',        # Main environment file
        ]
        
        # Track loaded variables for debugging
        self._loaded_vars = {}
        self._validation_errors = []
        
        if auto_load:
            self.load_env_files()
    
    def _find_project_root(self) -> Path:
        """Find the project root directory."""
        current = Path(__file__).parent
        while current.parent != current:
            if (current / 'app.py').exists() or (current / '.git').exists():
                return current
            current = current.parent
        return Path(__file__).parent.parent
    
    def load_env_files(self) -> None:
        """Load environment variables from .env files."""
        for env_file in self.env_files:
            if env_file.exists():
                self._load_env_file(env_file)
    
    def _load_env_file(self, env_file: Path) -> None:
        """Load a specific .env file."""
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    # Parse key=value pairs
                    if '=' not in line:
                        logger.warning(f"Invalid line in {env_file}:{line_num}: {line}")
                        continue
                    
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # Only set if not already in environment (env vars take precedence)
                    if key not in os.environ:
                        os.environ[key] = value
                        self._loaded_vars[key] = str(env_file)
            
            logger.info(f"Loaded environment variables from {env_file}")
            
        except Exception as e:
            logger.error(f"Failed to load {env_file}: {e}")
    
    def get_str(self, key: str, default: Optional[str] = None, required: bool = False) -> Optional[str]:
        """
        Get a string environment variable.
        
        Args:
            key: Environment variable name
            default: Default value if not found
            required: Whether the variable is required
            
        Returns:
            String value or default
        """
        value = os.getenv(key, default)
        
        if required and not value:
            error = f"Required environment variable {key} is not set"
            self._validation_errors.append(error)
            logger.error(error)
            return None
        
        return value
    
    def get_int(self, key: str, default: Optional[int] = None, required: bool = False) -> Optional[int]:
        """Get an integer environment variable."""
        value = os.getenv(key)
        
        if value is None:
            if required:
                error = f"Required environment variable {key} is not set"
                self._validation_errors.append(error)
                logger.error(error)
            return default
        
        try:
            return int(value)
        except ValueError:
            error = f"Environment variable {key}='{value}' is not a valid integer"
            self._validation_errors.append(error)
            logger.error(error)
            return default
    
    def get_float(self, key: str, default: Optional[float] = None, required: bool = False) -> Optional[float]:
        """Get a float environment variable."""
        value = os.getenv(key)
        
        if value is None:
            if required:
                error = f"Required environment variable {key} is not set"
                self._validation_errors.append(error)
                logger.error(error)
            return default
        
        try:
            return float(value)
        except ValueError:
            error = f"Environment variable {key}='{value}' is not a valid float"
            self._validation_errors.append(error)
            logger.error(error)
            return default
    
    def get_bool(self, key: str, default: Optional[bool] = None, required: bool = False) -> Optional[bool]:
        """Get a boolean environment variable."""
        value = os.getenv(key)
        
        if value is None:
            if required:
                error = f"Required environment variable {key} is not set"
                self._validation_errors.append(error)
                logger.error(error)
            return default
        
        # Convert string to boolean
        value_lower = value.lower()
        if value_lower in ('true', '1', 'yes', 'on', 'enabled'):
            return True
        elif value_lower in ('false', '0', 'no', 'off', 'disabled'):
            return False
        else:
            error = f"Environment variable {key}='{value}' is not a valid boolean"
            self._validation_errors.append(error)
            logger.error(error)
            return default
    
    def get_list(self, key: str, default: Optional[List[str]] = None, 
                 separator: str = ',', required: bool = False) -> Optional[List[str]]:
        """Get a list environment variable (comma-separated by default)."""
        value = os.getenv(key)
        
        if value is None:
            if required:
                error = f"Required environment variable {key} is not set"
                self._validation_errors.append(error)
                logger.error(error)
            return default or []
        
        # Split and clean up the list
        items = [item.strip() for item in value.split(separator) if item.strip()]
        return items
    
    def get_json(self, key: str, default: Optional[Dict] = None, required: bool = False) -> Optional[Dict]:
        """Get a JSON environment variable."""
        value = os.getenv(key)
        
        if value is None:
            if required:
                error = f"Required environment variable {key} is not set"
                self._validation_errors.append(error)
                logger.error(error)
            return default
        
        try:
            return json.loads(value)
        except json.JSONDecodeError as e:
            error = f"Environment variable {key} contains invalid JSON: {e}"
            self._validation_errors.append(error)
            logger.error(error)
            return default
    
    def get_url(self, key: str, default: Optional[str] = None, required: bool = False) -> Optional[str]:
        """Get a URL environment variable with basic validation."""
        value = self.get_str(key, default, required)
        
        if value and not (value.startswith('http://') or value.startswith('https://')):
            error = f"Environment variable {key}='{value}' is not a valid URL"
            self._validation_errors.append(error)
            logger.error(error)
            return default
        
        return value
    
    def get_path(self, key: str, default: Optional[str] = None, 
                 required: bool = False, must_exist: bool = False) -> Optional[Path]:
        """Get a file path environment variable."""
        value = self.get_str(key, default, required)
        
        if value is None:
            return None
        
        path = Path(value)
        
        # Make relative paths relative to project root
        if not path.is_absolute():
            path = self.project_root / path
        
        if must_exist and not path.exists():
            error = f"Path specified in {key}='{value}' does not exist: {path}"
            self._validation_errors.append(error)
            logger.error(error)
            return None
        
        return path
    
    def validate_required_vars(self, required_vars: List[str]) -> List[str]:
        """
        Validate that all required environment variables are set.
        
        Args:
            required_vars: List of required variable names
            
        Returns:
            List of missing variables
        """
        missing = []
        for var in required_vars:
            if not os.getenv(var):
                missing.append(var)
        
        return missing
    
    def get_validation_errors(self) -> List[str]:
        """Get all validation errors encountered."""
        return self._validation_errors.copy()
    
    def clear_validation_errors(self) -> None:
        """Clear validation errors."""
        self._validation_errors.clear()
    
    def get_loaded_vars_summary(self) -> Dict[str, str]:
        """Get summary of loaded variables and their sources."""
        return self._loaded_vars.copy()
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get comprehensive environment information."""
        return {
            'project_root': str(self.project_root),
            'env_files_checked': [str(f) for f in self.env_files],
            'env_files_loaded': [f for f, exists in 
                               [(str(f), f.exists()) for f in self.env_files] if exists],
            'loaded_vars_count': len(self._loaded_vars),
            'validation_errors_count': len(self._validation_errors),
            'environment_type': self.get_str('ENVIRONMENT', 'development'),
            'debug_mode': self.get_bool('DEBUG', False),
            'production_mode': self.get_str('ENVIRONMENT') == 'production',
        }


# Global instance
env_loader = EnvironmentLoader()

# Convenience functions for common environment variables
def get_environment() -> str:
    """Get the current environment (development, staging, production)."""
    return env_loader.get_str('ENVIRONMENT', 'development')

def is_development() -> bool:
    """Check if running in development mode."""
    return get_environment() == 'development'

def is_production() -> bool:
    """Check if running in production mode."""
    return get_environment() == 'production'

def is_debug_enabled() -> bool:
    """Check if debug mode is enabled."""
    return env_loader.get_bool('DEBUG', False)

def get_secret_key() -> str:
    """Get the application secret key."""
    return env_loader.get_str('SECRET_KEY', 'change-this-in-production', required=True)

def get_database_url() -> str:
    """Get the database URL."""
    return env_loader.get_str('DATABASE_URL', 'sqlite:///data/ldr.db')

def get_host() -> str:
    """Get the server host."""
    return env_loader.get_str('HOST', '0.0.0.0')

def get_port() -> int:
    """Get the server port."""
    return env_loader.get_int('PORT', 8765)

def get_log_level() -> str:
    """Get the logging level."""
    return env_loader.get_str('LOG_LEVEL', 'INFO')

def validate_environment() -> List[str]:
    """Validate the current environment configuration."""
    errors = []
    
    # Check required variables based on environment
    if is_production():
        required_vars = [
            'SECRET_KEY',
            'KITCO_SECRETS_KEY',
        ]
        missing = env_loader.validate_required_vars(required_vars)
        if missing:
            errors.extend([f"Required production variable missing: {var}" for var in missing])
        
        # Check for development values in production
        secret_key = get_secret_key()
        if secret_key and 'change-this' in secret_key.lower():
            errors.append("SECRET_KEY has default value in production")
    
    # Add any validation errors from the loader
    errors.extend(env_loader.get_validation_errors())
    
    return errors
