# ============================================================================
# Kitco Research AI - Development Dockerfile
# ============================================================================
# Development environment with all tools and hot-reload capabilities
# Includes testing, linting, debugging, and development utilities
# ============================================================================

FROM python:3.11-slim

# Set environment variables for development
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    FLASK_ENV=development \
    DEBUG=True

# Install system dependencies
RUN apt-get update && apt-get install -y \
    # Build tools
    build-essential \
    curl \
    git \
    # Required for Playwright
    libnss3 \
    libnspr4 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libxkbcommon0 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    libgbm1 \
    libxss1 \
    libasound2 \
    # Required for other dependencies
    libmagic1 \
    # Development tools
    vim \
    nano \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Create application directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY config/requirements-dev.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements-dev.txt

# Install Playwright browsers
RUN playwright install && \
    playwright install-deps

# Copy application code
COPY . .

# Create data directory structure
RUN python src/kitco_research_ai/setup_data_dir.py

# Create development user (optional, for better development experience)
RUN groupadd -r devuser && useradd -r -g devuser -s /bin/bash devuser && \
    chown -R devuser:devuser /app

# Switch to development user
USER devuser

# Expose port
EXPOSE 8765

# Development command with hot-reload
CMD ["python", "app.py", "--host=0.0.0.0", "--port=8765", "--debug"]
