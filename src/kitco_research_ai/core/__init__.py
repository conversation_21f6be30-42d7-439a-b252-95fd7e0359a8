"""
Core application components for Kitco Research AI.

This module provides the foundational components for the application including:
- Application factory and dependency injection
- Service layer abstractions
- Error handling and logging
- Health checks and monitoring
"""

from .application import create_application
from .container import Container
from .exceptions import (
    KRAError,
    ConfigurationError,
    DatabaseError,
    SearchError,
    ValidationError
)

__all__ = [
    "create_application",
    "Container",
    "KRAError",
    "ConfigurationError",
    "DatabaseError",
    "SearchError",
    "ValidationError"
]
