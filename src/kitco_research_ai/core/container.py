"""
Dependency injection container for Kitco Research AI.

This module provides a simple dependency injection container that manages
the lifecycle and dependencies of application components.
"""

import threading
from typing import Any, Callable, Dict, Optional, Type, TypeVar

from loguru import logger

from .exceptions import ConfigurationError

T = TypeVar('T')


class Container:
    """
    Simple dependency injection container with singleton support.
    
    This container manages the creation and lifecycle of application components,
    supporting both singleton and transient instances.
    """
    
    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._singletons: Dict[str, Any] = {}
        self._lock = threading.RLock()
    
    def register_singleton(self, service_type: Type[T], factory: Callable[[], T]) -> None:
        """
        Register a singleton service.
        
        Args:
            service_type: The type/interface of the service
            factory: Factory function to create the service instance
        """
        service_name = self._get_service_name(service_type)
        with self._lock:
            self._factories[service_name] = factory
            logger.debug(f"Registered singleton service: {service_name}")
    
    def register_transient(self, service_type: Type[T], factory: Callable[[], T]) -> None:
        """
        Register a transient service (new instance each time).
        
        Args:
            service_type: The type/interface of the service
            factory: Factory function to create the service instance
        """
        service_name = self._get_service_name(service_type)
        with self._lock:
            self._services[service_name] = factory
            logger.debug(f"Registered transient service: {service_name}")
    
    def register_instance(self, service_type: Type[T], instance: T) -> None:
        """
        Register a specific instance as a singleton.
        
        Args:
            service_type: The type/interface of the service
            instance: The service instance
        """
        service_name = self._get_service_name(service_type)
        with self._lock:
            self._singletons[service_name] = instance
            logger.debug(f"Registered service instance: {service_name}")
    
    def get(self, service_type: Type[T]) -> T:
        """
        Get a service instance.
        
        Args:
            service_type: The type/interface of the service
            
        Returns:
            Service instance
            
        Raises:
            ConfigurationError: If service is not registered
        """
        service_name = self._get_service_name(service_type)
        
        with self._lock:
            # Check if we have a singleton instance
            if service_name in self._singletons:
                return self._singletons[service_name]
            
            # Check if we have a singleton factory
            if service_name in self._factories:
                instance = self._factories[service_name]()
                self._singletons[service_name] = instance
                return instance
            
            # Check if we have a transient factory
            if service_name in self._services:
                return self._services[service_name]()
            
            raise ConfigurationError(
                f"Service not registered: {service_name}",
                error_code="SERVICE_NOT_REGISTERED",
                details={"service_type": service_name}
            )
    
    def has(self, service_type: Type[T]) -> bool:
        """
        Check if a service is registered.
        
        Args:
            service_type: The type/interface of the service
            
        Returns:
            True if service is registered
        """
        service_name = self._get_service_name(service_type)
        with self._lock:
            return (
                service_name in self._singletons or
                service_name in self._factories or
                service_name in self._services
            )
    
    def clear(self) -> None:
        """Clear all registered services."""
        with self._lock:
            self._services.clear()
            self._factories.clear()
            self._singletons.clear()
            logger.debug("Cleared all registered services")
    
    @staticmethod
    def _get_service_name(service_type: Type) -> str:
        """Get the service name from a type."""
        if hasattr(service_type, '__name__'):
            return service_type.__name__
        return str(service_type)


# Global container instance
_container: Optional[Container] = None
_container_lock = threading.RLock()


def get_container() -> Container:
    """Get the global container instance."""
    global _container
    if _container is None:
        with _container_lock:
            if _container is None:
                _container = Container()
    return _container


def reset_container() -> None:
    """Reset the global container (mainly for testing)."""
    global _container
    with _container_lock:
        if _container is not None:
            _container.clear()
        _container = None
