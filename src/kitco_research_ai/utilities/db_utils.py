import os
from functools import cache
from typing import Any, Dict

from loguru import logger
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from ..web.services.settings_manager import SettingsManager

# Database path.
DATA_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..", "..", "data"))
DB_PATH = os.path.join(DATA_DIR, "ldr.db")


@cache
def get_db_engine():
    """
    Returns:
        The singleton DB engine.
    """
    return create_engine(f"sqlite:///{DB_PATH}")


def get_db_session() -> Session:
    """
    Returns:
        A new DB session. Caller is responsible for closing it.
    """
    engine = get_db_engine()
    session_class = sessionmaker(bind=engine)
    return session_class()


@cache
def get_settings_manager() -> SettingsManager:
    """
    Returns:
        The singleton settings manager.

    """
    return SettingsManager(db_session=get_db_session())


def get_db_setting(
    key: str, default_value: Any | None = None
) -> str | Dict[str, Any] | None:
    """
    Get a setting from the database with fallback to default value

    Args:
        key: The setting key.
        default_value: If the setting is not found, it will return this instead.

    Returns:
        The setting value.

    """
    try:
        # Get settings manager which handles database access
        value = get_settings_manager().get_setting(key)

        if value is not None:
            return value
    except Exception:
        logger.exception(f"Error getting setting {key} from database")

    logger.warning(f"Could not find setting '{key}' in the database.")
    return default_value
