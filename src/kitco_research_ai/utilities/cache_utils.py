"""
Caching utilities for Kitco Research AI.

This module provides caching mechanisms to improve performance.
"""

import time
from collections import OrderedDict
from typing import Any, Dict, List, Optional, Union

from loguru import logger


class SearchResultsCache:
    """
    Cache for search results to avoid redundant searches.
    Uses LRU (Least Recently Used) eviction policy and TTL (Time To Live).
    """

    def __init__(self, max_size: int = 100, ttl: int = 3600):  # 1 hour TTL by default
        """
        Initialize the search results cache.

        Args:
            max_size: Maximum number of entries in the cache
            ttl: Time to live in seconds for cache entries
        """
        self.cache: OrderedDict[str, Any] = OrderedDict()
        self.max_size = max_size
        self.ttl = ttl
        self.access_times: OrderedDict[str, float] = OrderedDict()

    def get(self, query: str) -> Optional[List[Dict]]:
        """
        Get cached search results for a query.

        Args:
            query: The search query

        Returns:
            Cached search results or None if not found or expired
        """
        normalized_query = self._normalize_query(query)
        if normalized_query in self.cache:
            # Check if cache entry is still valid
            if time.time() - self.access_times[normalized_query] < self.ttl:
                # Move to end (most recently used) for LRU
                self.cache.move_to_end(normalized_query)
                self.access_times.move_to_end(normalized_query)
                self.access_times[normalized_query] = time.time()  # Update access time
                logger.debug(f"Cache hit for query: {query[:30]}...")
                return self.cache[normalized_query]
            else:
                # Remove expired entry
                logger.debug(f"Removing expired cache entry for query: {query[:30]}...")
                del self.cache[normalized_query]
                del self.access_times[normalized_query]
        return None

    def set(self, query: str, results: List[Dict]) -> None:
        """
        Cache search results for a query.

        Args:
            query: The search query
            results: The search results to cache
        """
        normalized_query = self._normalize_query(query)
        # Implement LRU eviction if cache is full
        if len(self.cache) >= self.max_size:
            # Remove least recently used item (first item in OrderedDict)
            oldest_key, _ = self.cache.popitem(last=False)
            del self.access_times[oldest_key]
            logger.debug(f"Cache full, evicting oldest entry: {oldest_key[:30]}...")

        self.cache[normalized_query] = results
        self.access_times[normalized_query] = time.time()
        logger.debug(f"Cached results for query: {query[:30]}...")

    def clear(self) -> None:
        """Clear the entire cache."""
        self.cache.clear()
        self.access_times.clear()
        logger.debug("Search results cache cleared")

    def _normalize_query(self, query: str) -> str:
        """
        Normalize query to improve cache hit rate.

        Args:
            query: The original query

        Returns:
            Normalized query string
        """
        return query.lower().strip()


class FindingsCache:
    """
    Cache for research findings to improve synthesis performance.
    """

    def __init__(self, max_size: int = 50, ttl: int = 7200):  # 2 hour TTL by default
        """
        Initialize the findings cache.

        Args:
            max_size: Maximum number of entries in the cache
            ttl: Time to live in seconds for cache entries
        """
        self.cache: OrderedDict[str, Any] = OrderedDict()
        self.max_size = max_size
        self.ttl = ttl
        self.access_times: OrderedDict[str, float] = OrderedDict()

    def get(self, key: str) -> Optional[Any]:
        """
        Get cached findings for a key.

        Args:
            key: The cache key (usually query or query hash)

        Returns:
            Cached findings or None if not found or expired
        """
        if key in self.cache:
            # Check if cache entry is still valid
            if time.time() - self.access_times[key] < self.ttl:
                # Move to end (most recently used) for LRU
                self.cache.move_to_end(key)
                self.access_times.move_to_end(key)
                self.access_times[key] = time.time()  # Update access time
                return self.cache[key]
            else:
                # Remove expired entry
                del self.cache[key]
                del self.access_times[key]
        return None

    def set(self, key: str, findings: Any) -> None:
        """
        Cache findings for a key.

        Args:
            key: The cache key (usually query or query hash)
            findings: The findings to cache
        """
        # Implement LRU eviction if cache is full
        if len(self.cache) >= self.max_size:
            # Remove least recently used item (first item in OrderedDict)
            oldest_key, _ = self.cache.popitem(last=False)
            del self.access_times[oldest_key]

        self.cache[key] = findings
        self.access_times[key] = time.time()

    def clear(self) -> None:
        """Clear the entire cache."""
        self.cache.clear()
        self.access_times.clear()
