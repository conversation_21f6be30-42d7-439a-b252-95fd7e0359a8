"""URL utility functions for the Kitco Research AI application."""

import logging

logger = logging.getLogger(__name__)


def normalize_url(raw_url: str) -> str:
    """
    Normalize a URL to ensure it has a proper scheme and format.

    Args:
        raw_url: The raw URL string to normalize

    Returns:
        A properly formatted URL string

    Examples:
        >>> normalize_url("localhost:11434")
        'http://localhost:11434'
        >>> normalize_url("https://example.com:11434")
        'https://example.com:11434'
        >>> normalize_url("http:example.com")
        'http://example.com'
    """
    if not raw_url:
        raise ValueError("URL cannot be empty")

    # Clean up the URL
    raw_url = raw_url.strip()

    # First check if the URL already has a proper scheme
    if raw_url.startswith(("http://", "https://")):
        return raw_url

    # Handle case where URL is malformed like "http:hostname" (missing //)
    if raw_url.startswith(("http:", "https:")) and not raw_url.startswith(
        ("http://", "https://")
    ):
        scheme = raw_url.split(":", 1)[0]
        rest = raw_url.split(":", 1)[1]
        return f"{scheme}://{rest}"

    # Handle URLs that start with //
    if raw_url.startswith("//"):
        # Remove the // and process
        raw_url = raw_url[2:]

    # At this point, we should have hostname:port or just hostname
    # Determine if this is localhost or an external host
    hostname = raw_url.split(":")[0].split("/")[0]
    is_localhost = hostname in ("localhost", "127.0.0.1", "[::1]", "0.0.0.0")

    # Use http for localhost, https for external hosts
    scheme = "http" if is_localhost else "https"

    return f"{scheme}://{raw_url}"
