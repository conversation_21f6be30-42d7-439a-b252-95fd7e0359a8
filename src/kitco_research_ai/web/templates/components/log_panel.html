<div class="collapsible-log-panel">
    <div class="log-panel-header" id="log-panel-toggle">
        <i class="fas fa-chevron-right toggle-icon"></i>
        <span>Research Logs</span>
        <span class="log-indicator" id="log-indicator">0</span>
    </div>
    <div class="log-panel-content collapsed" id="log-panel-content">
        <div class="log-controls">
            <div class="log-filter">
                <div class="filter-buttons">
                    <button class="small-btn selected" onclick="window.filterLogsByType('all')">All</button>
                    <button class="small-btn" onclick="window.filterLogsByType('milestone')">Milestones</button>
                    <button class="small-btn" onclick="window.filterLogsByType('info')">Info</button>
                    <button class="small-btn" onclick="window.filterLogsByType('error')">Errors</button>
                </div>
            </div>
        </div>
        <div class="console-log" id="console-log-container">
            <!-- Logs will be added here dynamically -->
            <div class="empty-log-message">No logs yet. Research logs will appear here as they occur.</div>
        </div>
    </div>
</div>

<!-- Template for console log entries -->
<template id="console-log-entry-template">
    <div class="console-log-entry">
        <span class="log-timestamp"></span>
        <span class="log-badge"></span>
        <span class="log-message"></span>
    </div>
</template> 