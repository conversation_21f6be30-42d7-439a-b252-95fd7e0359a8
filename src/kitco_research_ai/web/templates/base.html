<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Kitco Research AI{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github-dark.min.css" integrity="sha512-rO+olRTkcf/42UQVblSWtuKmQNcunYVnxgAJjSBHE25OWDQsrOnKUG2xDet4eA2NDbsrD5xf4pMkZbB0k6jmRA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        {% include 'components/sidebar.html' %}

        <!-- Main Content -->
        <main class="main-content">
            {% block content %}{% endblock %}

            <!-- Collapsible Log Panel is included in specific pages -->
        </main>
    </div>

    <!-- Mobile Tab Bar -->
    {% include 'components/mobile_nav.html' %}

    <!-- Common Templates -->
    {% block templates %}{% endblock %}

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.4.1/socket.io.min.js" integrity="sha512-XMXVpLLwEkCtkNNSKSyZhC+3+ZCEf0b/rDNzJtHw0Z8cqUhYVw2JtROcQOuXkp7/84I9d/82DFG+8yZYC3VtWA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/lib/marked.umd.min.js" integrity="sha512-mfmrAr62ob+CiJ4heDp0LrREPbOUAoaCBgQRGnO2pOqI9MvIqMmqKClpqnMjn2mpmMClp5ZZeWPELFQNMNd2Yw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js" integrity="sha512-bgHRAiTjGrzHzLyKOnpFvaEpGzJet3z4tZnXGjpsCcqOnAH6VGUx9frc5bcIhKTVLEiCO6vEhNAgx5jtLUYrfA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" integrity="sha512-qZvrmS2ekKPF2mSznTQsxqPgnpkI4DNTlrdUmTzrDgektczlKNRRhy5X5AAOnx5S09ydFYWWNSfcEqDTTHgtNA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" integrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoeqMV/TJlSKda6FXzoEyYGjTe+vXA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <!-- Core JS -->
    <script src="{{ url_for('research.serve_static', path='js/services/sanitizer.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/formatting.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/ui.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/api.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/socket.js') }}"></script>

    <!-- Shared Components -->
    <script src="{{ url_for('research.serve_static', path='js/components/logpanel.js') }}"></script>

    <!-- Page-specific Components -->
    {% block component_scripts %}{% endblock %}

    <!-- Page-specific JS -->
    {% block page_scripts %}{% endblock %}

    <script>
        // Configure marked to not use eval
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                headerIds: false,
                mangle: false,
                smartypants: false
            });
        }

        // Configure html2canvas to avoid using eval if possible
        if (typeof html2canvas !== 'undefined') {
            window.html2canvas_noSandbox = true;
        }
    </script>

    <script src="/research/static/js/components/settings_sync.js"></script>
</body>
</html>
