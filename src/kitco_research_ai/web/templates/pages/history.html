{% extends "base.html" %}

{% set active_page = 'history' %}

{% block title %}Research History - Kitco Research AI{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/components/history.css') }}">
{% endblock %}

{% block content %}
<div class="page active" id="history">
    <div class="page-header">
        <h1>Research History</h1>
    </div>
    <div class="card">
        <div class="card-content">
            <div class="search-controls" style="margin-bottom: 1rem;">
                <input type="text" id="history-search" placeholder="Search history..." class="search-input">
                <button id="clear-history-btn" class="btn btn-danger" style="display: none;">
                    <i class="fas fa-trash-alt"></i> Clear All
                </button>
            </div>
            <div class="history-list" id="history-items">
                <!-- Will be populated dynamically -->
                <div class="loading-spinner centered">
                    <div class="spinner"></div>
                </div>
            </div>
            <div id="history-empty-message" class="empty-state" style="display: none;">
                <i class="fas fa-history empty-icon"></i>
                <p>No research history found.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block templates %}
<!-- Template is no longer needed since we build items dynamically in JavaScript -->
{% endblock %}

{% block page_scripts %}
<!-- Load utilities first -->
<script src="{{ url_for('research.serve_static', path='js/services/ui.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/services/formatting.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/services/api.js') }}"></script>
<!-- Then load the component -->
<script src="{{ url_for('research.serve_static', path='js/components/history.js') }}"></script>
{% endblock %} 