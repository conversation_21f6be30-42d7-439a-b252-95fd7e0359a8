{% extends "base.html" %}

{% block title %}Research Details - Kitco Research AI{% endblock %}

{% block content %}
<div class="page active" id="research-details">
    <div class="page-header">
        <div class="results-header">
            <h1>Research Details</h1>
            <div class="results-actions">
                <button class="btn btn-outline" id="back-to-history-from-details"><i class="fas fa-arrow-left"></i> Back to History</button>
            </div>
        </div>
    </div>
    <div class="card">
        <div class="card-content">
            <div class="research-metadata">
                <div class="metadata-item">
                    <span class="metadata-label">Query:</span>
                    <span id="detail-query" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Status:</span>
                    <span id="detail-status" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Mode:</span>
                    <span id="detail-mode" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Progress:</span>
                    <div class="detail-progress-container">
                        <div class="detail-progress-bar">
                            <div class="detail-progress-fill" id="detail-progress-fill"></div>
                        </div>
                        <span id="detail-progress-percentage">0%</span>
                    </div>
                </div>
            </div>

            <div class="research-log-container">
                <h3>Research Progress Log</h3>
                <div class="research-log" id="research-log">
                    <!-- Will be populated dynamically -->
                    <div class="loading-spinner centered">
                        <div class="spinner"></div>
                    </div>
                </div>
            </div>

            <div class="detail-actions" id="detail-actions">
                <!-- Conditionally shown based on research status -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block templates %}
<template id="log-entry-template">
    <div class="log-entry">
        <div class="log-entry-time"></div>
        <div class="log-entry-content">
            <div class="log-entry-message"></div>
            <div class="log-entry-progress"></div>
        </div>
    </div>
</template>
{% endblock %}

{% block page_scripts %}
<script src="{{ url_for('research.serve_static', path='js/components/details.js') }}"></script>
{% endblock %} 