/**
 * Results Component
 * Handles the display of research results
 */
(function() {
    // DOM Elements
    let resultsContainer = null;
    let exportBtn = null;
    let pdfBtn = null;
    let researchId = null;
    let researchData = null;
    
    /**
     * Initialize the results component
     */
    function initializeResults() {
        // Get DOM elements
        resultsContainer = document.getElementById('results-content');
        exportBtn = document.getElementById('export-markdown-btn');
        pdfBtn = document.getElementById('download-pdf-btn');
        
        if (!resultsContainer) {
            console.error('Results container not found');
            return;
        }
        
        console.log('Results component initialized');
        
        // Get research ID from URL
        researchId = getResearchIdFromUrl();
        
        if (!researchId) {
            showError('Research ID not found in URL');
            return;
        }
        
        // Set up event listeners
        setupEventListeners();
        
        // Load research results
        loadResearchResults();
        
        // Note: Log panel is now automatically initialized by logpanel.js
        // No need to manually initialize it here
    }
    
    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Export button
        if (exportBtn) {
            exportBtn.addEventListener('click', handleExport);
        }
        
        // PDF button
        if (pdfBtn) {
            pdfBtn.addEventListener('click', handlePdfExport);
        }

        // Back to history button
        const backBtn = document.getElementById('back-to-history');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                window.location.href = '/research/history';
            });
        }
    }
    
    /**
     * Get research ID from URL
     * @returns {string|null} Research ID
     */
    function getResearchIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/research\/results\/(\d+)/);
        return match ? match[1] : null;
    }
    
    /**
     * Load research results from API
     */
    async function loadResearchResults() {
        try {
            // Show loading state
            resultsContainer.innerHTML = '<div class="text-center my-5"><i class="fas fa-spinner fa-pulse"></i><p class="mt-3">Loading research results...</p></div>';
            
            // Fetch result from API
            const response = await fetch(`/research/api/report/${researchId}`);
            
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            
            const responseData = await response.json();
            console.log('Original API response:', responseData);
            
            // Store data for export
            researchData = responseData;
            
            // Check if we have data to display
            if (!responseData) {
                throw new Error('No data received from server');
            }
            
            // Use the API metadata directly
            if (responseData.metadata && typeof responseData.metadata === 'object') {
                console.log('Using metadata directly from API response:', responseData.metadata);
                populateMetadataFromApiResponse(responseData);
            } else {
                // Fallback to content extraction if no metadata in response
                populateMetadata(responseData);
            }
            
            // Render the content
            if (responseData.content && typeof responseData.content === 'string') {
                console.log('Rendering content from API response');
                renderResults(responseData.content);
            } else {
                // Try to find content in other response formats
                console.log('No direct content found, trying to find content in response');
                findAndRenderContent(responseData);
            }
            
            // Enable export buttons
            if (exportBtn) exportBtn.disabled = false;
            if (pdfBtn) pdfBtn.disabled = false;
            
        } catch (error) {
            console.error('Error loading research results:', error);
            showError(`Error loading research results: ${error.message}`);
            
            // Disable export buttons
            if (exportBtn) exportBtn.disabled = true;
            if (pdfBtn) pdfBtn.disabled = true;
        }
    }
    
    /**
     * Populate metadata directly from API response metadata
     * @param {Object} data - API response with metadata
     */
    function populateMetadataFromApiResponse(data) {
        const metadata = data.metadata || {};
        console.log('Using API response metadata:', metadata);
        
        // Query field
        const queryElement = document.getElementById('result-query');
        if (queryElement) {
            // Use query from metadata or content title
            const query = metadata.query || metadata.title || data.query || 'Untitled Research';
            console.log('Setting query to:', query);
            queryElement.textContent = query;
        }
        
        // Generated date field
        const dateElement = document.getElementById('result-date');
        if (dateElement) {
            let dateStr = 'Unknown date';
            
            // Try multiple sources for the timestamp - first from the API response directly, then from metadata
            const timestamp = data.created_at || data.timestamp || data.date || 
                            metadata.created_at || metadata.timestamp || metadata.date;
            
            console.log('Found timestamp:', timestamp);
            
            if (timestamp) {
                if (window.formatting && typeof window.formatting.formatDate === 'function') {
                    dateStr = window.formatting.formatDate(timestamp);
                    console.log('Formatting timestamp with formatter:', timestamp, '→', dateStr);
                } else {
                    try {
                        const date = new Date(timestamp);
                        dateStr = date.toLocaleString();
                        console.log('Formatting timestamp with toLocaleString:', timestamp, '→', dateStr);
                    } catch (e) {
                        console.error('Error parsing date:', e);
                    }
                }
                
                // Add duration if available - format as "Xm Ys" for values over 60 seconds
                if (metadata.duration || metadata.duration_seconds || data.duration_seconds) {
                    const durationSeconds = parseInt(metadata.duration || metadata.duration_seconds || data.duration_seconds, 10);
                    
                    if (!isNaN(durationSeconds)) {
                        let durationStr;
                        if (durationSeconds < 60) {
                            durationStr = `${durationSeconds}s`;
                        } else {
                            const minutes = Math.floor(durationSeconds / 60);
                            const seconds = durationSeconds % 60;
                            durationStr = `${minutes}m ${seconds}s`;
                        }
                        dateStr += ` (${durationStr})`;
                    }
                }
            }
            
            console.log('Setting date to:', dateStr);
            dateElement.textContent = dateStr;
        }
        
        // Mode field
        const modeElement = document.getElementById('result-mode');
        if (modeElement) {
            // Get mode from metadata or main response
            let mode = metadata.mode || metadata.research_mode || metadata.type || 
                      data.mode || data.research_mode || data.type;
            
            // Detect if this is a detailed report based on content structure
            if (!mode && data.content) {
                if (data.content.toLowerCase().includes('table of contents') || 
                    data.content.match(/^#.*\n+##.*\n+###/m)) {
                    mode = 'detailed';
                } else {
                    mode = 'quick';
                }
            }
            
            // Format mode using available formatter
            if (window.formatting && typeof window.formatting.formatMode === 'function') {
                mode = window.formatting.formatMode(mode);
                console.log('Formatted mode:', mode);
            }
            
            console.log('Setting mode to:', mode || 'Quick');
            modeElement.textContent = mode || 'Quick';
        }
    }
    
    /**
     * Find and render content from various response formats
     * @param {Object} data - Research data to extract content from
     */
    function findAndRenderContent(data) {
        if (data.content && typeof data.content === 'string') {
            // Direct content property (newer format)
            console.log('Rendering from data.content');
            renderResults(data.content);
        } else if (data.research && data.research.content) {
            // Nested content in research object (older format)
            console.log('Rendering from data.research.content');
            renderResults(data.research.content);
        } else if (data.report && typeof data.report === 'string') {
            // Report format
            console.log('Rendering from data.report');
            renderResults(data.report);
        } else if (data.results && data.results.content) {
            // Results with content field
            console.log('Rendering from data.results.content');
            renderResults(data.results.content);
        } else if (data.results && typeof data.results === 'string') {
            // Results as direct string
            console.log('Rendering from data.results string');
            renderResults(data.results);
        } else if (typeof data === 'string') {
            // Plain string format
            console.log('Rendering from string data');
            renderResults(data);
        } else {
            // Look for any property that might contain the content
            const contentProps = ['markdown', 'text', 'summary', 'output', 'research_output'];
            let foundContent = false;
            
            for (const prop of contentProps) {
                if (data[prop] && typeof data[prop] === 'string') {
                    console.log(`Rendering from data.${prop}`);
                    renderResults(data[prop]);
                    foundContent = true;
                    break;
                }
            }
            
            if (!foundContent) {
                // Last resort: try to render the entire data object
                console.log('No clear content found, rendering entire data object');
                renderResults(data);
            }
        }
    }
    
    /**
     * Populate metadata fields with information from the research data
     * @param {Object} data - Research data with metadata
     */
    function populateMetadata(data) {
        // Debug the data structure
        console.log('API response data:', data);
        console.log('Data type:', typeof data);
        console.log('Available top-level keys:', Object.keys(data));
        
        // Direct extraction from content
        if (data.content && typeof data.content === 'string') {
            console.log('Attempting to extract metadata from content');
            
            // Extract the query from content first line or header
            // Avoid matching "Table of Contents" as query
            const queryMatch = data.content.match(/^#\s*([^\n]+)/m) || // First heading
                             data.content.match(/Query:\s*([^\n]+)/i) || // Explicit query label
                             data.content.match(/Question:\s*([^\n]+)/i) || // Question label
                             data.content.match(/^([^\n#]+)(?=\n)/); // First line if not starting with #
                             
            if (queryMatch && queryMatch[1] && !queryMatch[1].toLowerCase().includes('table of contents')) {
                const queryElement = document.getElementById('result-query');
                if (queryElement) {
                    const extractedQuery = queryMatch[1].trim();
                    console.log('Extracted query from content:', extractedQuery);
                    queryElement.textContent = extractedQuery;
                }
            } else {
                // Try to find the second heading if first was "Table of Contents"
                const secondHeadingMatch = data.content.match(/^#\s*([^\n]+)[\s\S]*?^##\s*([^\n]+)/m);
                if (secondHeadingMatch && secondHeadingMatch[2]) {
                    const queryElement = document.getElementById('result-query');
                    if (queryElement) {
                        const extractedQuery = secondHeadingMatch[2].trim();
                        console.log('Extracted query from second heading:', extractedQuery);
                        queryElement.textContent = extractedQuery;
                    }
                }
            }
            
            // Extract generated date/time - Try multiple formats
            const dateMatch = data.content.match(/Generated at:\s*([^\n]+)/i) || 
                           data.content.match(/Date:\s*([^\n]+)/i) ||
                           data.content.match(/Generated:\s*([^\n]+)/i) ||
                           data.content.match(/Created:\s*([^\n]+)/i);
                           
            if (dateMatch && dateMatch[1]) {
                const dateElement = document.getElementById('result-date');
                if (dateElement) {
                    const extractedDate = dateMatch[1].trim();
                    console.log('Extracted date from content:', extractedDate);
                    
                    // Format the date using the available formatter
                    let formattedDate = extractedDate;
                    if (window.formatting && typeof window.formatting.formatDate === 'function') {
                        formattedDate = window.formatting.formatDate(extractedDate);
                        console.log('Date formatted using formatter:', formattedDate);
                    }
                    
                    dateElement.textContent = formattedDate || new Date().toLocaleString();
                }
            }
            
            // Extract mode
            const modeMatch = data.content.match(/Mode:\s*([^\n]+)/i) || 
                            data.content.match(/Research type:\s*([^\n]+)/i);
            
            if (modeMatch && modeMatch[1]) {
                const modeElement = document.getElementById('result-mode');
                if (modeElement) {
                    const extractedMode = modeMatch[1].trim();
                    console.log('Extracted mode from content:', extractedMode);
                    
                    // Format mode using available formatter
                    let formattedMode = extractedMode;
                    if (window.formatting && typeof window.formatting.formatMode === 'function') {
                        formattedMode = window.formatting.formatMode(extractedMode);
                        console.log('Mode formatted using formatter:', formattedMode);
                    }
                    
                    modeElement.textContent = formattedMode || 'Standard';
                }
            } else {
                // Detect mode based on content structure and keywords
                const modeElement = document.getElementById('result-mode');
                if (modeElement) {
                    if (data.content.toLowerCase().includes('table of contents') || 
                        data.content.toLowerCase().includes('detailed report') ||
                        data.content.match(/^#.*\n+##.*\n+###/m)) { // Has H1, H2, H3 structure
                        modeElement.textContent = 'Detailed';
                    } else if (data.content.toLowerCase().includes('quick research') || 
                              data.content.toLowerCase().includes('summary')) {
                        modeElement.textContent = 'Quick';
                    } else {
                        modeElement.textContent = 'Standard'; // Better default
                    }
                }
            }
            
            return; // Exit early since we've handled extraction from content
        }
        
        // Also check the metadata field which likely contains the actual metadata
        const metadata = data.metadata || {};
        console.log('Metadata object:', metadata);
        if (metadata) {
            console.log('Metadata keys:', Object.keys(metadata));
        }
        
        // Extract research object if nested
        const researchData = data.research || data;
        
        // Debug nested structure if exists
        if (data.research) {
            console.log('Nested research data:', data.research);
            console.log('Research keys:', Object.keys(data.research));
        }
        
        // Query field
        const queryElement = document.getElementById('result-query');
        if (queryElement) {
            // Try different possible locations for query data
            let query = 'Unknown query';
            
            if (metadata.query) {
                query = metadata.query;
            } else if (metadata.title) {
                query = metadata.title;
            } else if (researchData.query) {
                query = researchData.query;
            } else if (researchData.prompt) {
                query = researchData.prompt;
            } else if (researchData.title) {
                query = researchData.title;
            } else if (researchData.question) {
                query = researchData.question;
            } else if (researchData.input) {
                query = researchData.input;
            }
            
            console.log('Setting query to:', query);
            queryElement.textContent = query;
        }
        
        // Generated date field
        const dateElement = document.getElementById('result-date');
        if (dateElement) {
            let dateStr = 'Unknown date';
            let timestampField = null;
            
            // Try different possible date fields
            if (metadata.created_at) {
                timestampField = metadata.created_at;
            } else if (metadata.timestamp) {
                timestampField = metadata.timestamp;
            } else if (metadata.date) {
                timestampField = metadata.date;
            } else if (researchData.timestamp) {
                timestampField = researchData.timestamp;
            } else if (researchData.created_at) {
                timestampField = researchData.created_at;
            } else if (researchData.date) {
                timestampField = researchData.date;
            } else if (researchData.time) {
                timestampField = researchData.time;
            }
            
            // Format the date using the available formatter
            if (timestampField) {
                if (window.formatting && typeof window.formatting.formatDate === 'function') {
                    dateStr = window.formatting.formatDate(timestampField);
                    console.log('Using formatter for timestamp:', timestampField, '→', dateStr);
                } else {
                    try {
                        const date = new Date(timestampField);
                        dateStr = date.toLocaleString();
                        console.log('Using timestamp:', timestampField, '→', dateStr);
                    } catch (e) {
                        console.error('Error parsing date:', timestampField, e);
                    }
                }
            }
            
            // Add duration if available
            if (metadata.duration) {
                dateStr += ` (${metadata.duration} seconds)`;
            } else if (metadata.duration_seconds) {
                dateStr += ` (${metadata.duration_seconds} seconds)`;
            } else if (researchData.duration) {
                dateStr += ` (${researchData.duration} seconds)`;
            }
            
            console.log('Setting date to:', dateStr);
            dateElement.textContent = dateStr;
        }
        
        // Mode field
        const modeElement = document.getElementById('result-mode');
        if (modeElement) {
            let mode = 'Quick'; // Default to Quick
            
            if (metadata.mode) {
                mode = metadata.mode;
            } else if (metadata.research_mode) {
                mode = metadata.research_mode;
            } else if (metadata.type) {
                mode = metadata.type;
            } else if (researchData.mode) {
                mode = researchData.mode;
            } else if (researchData.research_mode) {
                mode = researchData.research_mode;
            } else if (researchData.type) {
                mode = researchData.type;
            }
            
            // Format mode using available formatter
            if (window.formatting && typeof window.formatting.formatMode === 'function') {
                mode = window.formatting.formatMode(mode);
            }
            
            console.log('Setting mode to:', mode);
            modeElement.textContent = mode;
        }
    }
    
    /**
     * Render research results in the container
     * @param {Object|string} data - Research data to render
     */
    function renderResults(data) {
        try {
            // Clear container
            resultsContainer.innerHTML = '';
            
            // Determine the content to render
            let content = '';
            
            if (typeof data === 'string') {
                // Direct string content
                content = data;
            } else if (data.markdown) {
                // Markdown content
                content = data.markdown;
            } else if (data.html) {
                // HTML content
                resultsContainer.innerHTML = data.html;
                return; // Return early since we've set HTML directly
            } else if (data.text) {
                // Text content
                content = data.text;
            } else if (data.summary) {
                // Summary content
                content = data.summary;
            } else if (data.results) {
                // Results array (old format)
                if (Array.isArray(data.results)) {
                    content = data.results.join('\n\n');
                } else {
                    content = JSON.stringify(data.results, null, 2);
                }
            } else {
                // Last resort: stringify the entire object
                content = JSON.stringify(data, null, 2);
            }
            
            // Render the content as Markdown if possible
            if (window.ui && window.ui.renderMarkdown) {
                const renderedHtml = window.ui.renderMarkdown(content);
                resultsContainer.innerHTML = renderedHtml;
            } else {
                // Fall back to basic formatting
                content = content.replace(/\n/g, '<br>');
                resultsContainer.innerHTML = `<div class="markdown-content">${content}</div>`;
            }
            
            // Add syntax highlighting if Prism is available
            if (window.Prism) {
                window.Prism.highlightAllUnder(resultsContainer);
            }
            
        } catch (error) {
            console.error('Error rendering results:', error);
            showError(`Error rendering results: ${error.message}`);
        }
    }
    
    /**
     * Show error message in the results container
     * @param {string} message - Error message
     */
    function showError(message) {
        resultsContainer.innerHTML = `
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
            <p class="text-center mt-3">
                <a href="/research" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back to Research
                </a>
            </p>
        `;
    }
    
    /**
     * Handle export button click
     */
    function handleExport() {
        try {
            if (!researchData) {
                throw new Error('No research data available');
            }
            
            // Get metadata from DOM (which should be populated by now)
            const query = document.getElementById('result-query')?.textContent || 'Unknown query';
            const generated = document.getElementById('result-date')?.textContent || 'Unknown date';
            const mode = document.getElementById('result-mode')?.textContent || 'Quick';
            
            // Create markdown header with metadata
            let markdownHeader = `# Research Results: ${query}\n\n`;
            markdownHeader += `- **Generated:** ${generated}\n`;
            markdownHeader += `- **Mode:** ${mode}\n\n`;
            markdownHeader += `---\n\n`;
            
            // Extract the content to export
            let markdownContent = '';
            
            // Try to extract the markdown content from various possible locations
            if (typeof researchData === 'string') {
                markdownContent = researchData;
            } else {
                // Check for content in standard locations
                const contentProps = [
                    'content',
                    'report',
                    'markdown',
                    'text',
                    'summary',
                    'output',
                    'research_output'
                ];
                
                let found = false;
                
                // First check direct properties
                for (const prop of contentProps) {
                    if (researchData[prop] && typeof researchData[prop] === 'string') {
                        markdownContent = researchData[prop];
                        console.log(`Using ${prop} for markdown content`);
                        found = true;
                        break;
                    }
                }
                
                // Then check nested properties
                if (!found && researchData.research) {
                    for (const prop of contentProps) {
                        if (researchData.research[prop] && typeof researchData.research[prop] === 'string') {
                            markdownContent = researchData.research[prop];
                            console.log(`Using research.${prop} for markdown content`);
                            found = true;
                            break;
                        }
                    }
                }
                
                // Check results property
                if (!found && researchData.results) {
                    if (typeof researchData.results === 'string') {
                        markdownContent = researchData.results;
                        console.log('Using results string for markdown content');
                    } else {
                        for (const prop of contentProps) {
                            if (researchData.results[prop] && typeof researchData.results[prop] === 'string') {
                                markdownContent = researchData.results[prop];
                                console.log(`Using results.${prop} for markdown content`);
                                found = true;
                                break;
                            }
                        }
                    }
                }
                
                // Last resort
                if (!markdownContent) {
                    console.warn('Could not extract markdown content, using JSON');
                    markdownContent = "```json\n" + JSON.stringify(researchData, null, 2) + "\n```";
                }
            }
            
            // Combine header and content
            const fullMarkdown = markdownHeader + markdownContent;
            
            // Create blob and trigger download
            const blob = new Blob([fullMarkdown], { type: 'text/markdown' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `research_${researchId}.md`;
            
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
        } catch (error) {
            console.error('Error exporting markdown:', error);

            // Use toast notification if available, otherwise fallback to alert
            if (window.ui && window.ui.showMessage) {
                window.ui.showMessage(`Error exporting markdown: ${error.message}`, 'error', 5000);
            } else {
                alert(`Error exporting markdown: ${error.message}`);
            }
        }
    }
    
    /**
     * Handle PDF export button click
     */
    function handlePdfExport() {
        try {
            if (!researchData) {
                throw new Error('No research data available');
            }
            
            console.log('PDF export initiated for research ID:', researchId);
            
            // Show loading indicator
            pdfBtn.disabled = true;
            pdfBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generating PDF...';
            
            // Get metadata from DOM (which should be populated correctly by now)
            const title = document.getElementById('result-query')?.textContent || `Research ${researchId}`;
            console.log('Using title for PDF:', title);
            
            // Check if PDF service is available
            if (window.pdfService && window.pdfService.downloadPdf) {
                console.log('PDF service available, calling downloadPdf');
                
                // Add the metadata to the researchData for PDF generation
                const pdfData = {
                    ...researchData,
                    title: title,
                    query: title,
                    metadata: {
                        title: title,
                        date: document.getElementById('result-date')?.textContent || 'Unknown date',
                        mode: document.getElementById('result-mode')?.textContent || 'Standard'
                    }
                };
                
                // Use the PDF service to generate and download the PDF
                window.pdfService.downloadPdf(pdfData, researchId)
                    .then(() => {
                        console.log('PDF generated successfully');
                        // Reset button
                        pdfBtn.disabled = false;
                        pdfBtn.innerHTML = '<i class="fas fa-file-pdf"></i> Download PDF';
                    })
                    .catch(error => {
                        console.error('Error generating PDF:', error);

                        // Use toast notification if available, otherwise fallback to alert
                        if (window.ui && window.ui.showMessage) {
                            window.ui.showMessage(`Error generating PDF: ${error.message || 'Unknown error'}`, 'error', 5000);
                        } else {
                            alert(`Error generating PDF: ${error.message || 'Unknown error'}`);
                        }

                        // Reset button
                        pdfBtn.disabled = false;
                        pdfBtn.innerHTML = '<i class="fas fa-file-pdf"></i> Download PDF';
                    });
            } else {
                console.error('PDF service not available');
                throw new Error('PDF service not available');
            }
            
        } catch (error) {
            console.error('Error exporting PDF:', error);

            // Use toast notification if available, otherwise fallback to alert
            if (window.ui && window.ui.showMessage) {
                window.ui.showMessage(`Error exporting PDF: ${error.message || 'Unknown error'}`, 'error', 5000);
            } else {
                alert(`Error exporting PDF: ${error.message || 'Unknown error'}`);
            }

            // Reset button
            if (pdfBtn) {
                pdfBtn.disabled = false;
                pdfBtn.innerHTML = '<i class="fas fa-file-pdf"></i> Download PDF';
            }
        }
    }
    
    // Initialize on DOM content loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeResults);
    } else {
        initializeResults();
    }
})(); 