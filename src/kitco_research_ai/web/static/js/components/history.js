/**
 * History Component
 * Manages the display and interaction with research history
 */
(function() {
    // DOM Elements
    let historyContainer = null;
    let searchInput = null;
    let clearHistoryBtn = null;
    let historyEmptyMessage = null;
    
    // Component state
    let historyItems = [];
    let filteredItems = [];
    
    // Fallback UI utilities in case main UI utils aren't loaded
    const uiUtils = {
        showSpinner: function(container, message) {
            if (window.ui && window.ui.showSpinner) {
                return window.ui.showSpinner(container, message);
            }
            
            // Fallback implementation
            if (!container) container = document.body;
            const spinnerHtml = `
                <div class="loading-spinner centered">
                    <div class="spinner"></div>
                    ${message ? `<div class="spinner-message">${message}</div>` : ''}
                </div>
            `;
            container.innerHTML = spinnerHtml;
        },
        
        hideSpinner: function(container) {
            if (window.ui && window.ui.hideSpinner) {
                return window.ui.hideSpinner(container);
            }
            
            // Fallback implementation
            if (!container) container = document.body;
            const spinner = container.querySelector('.loading-spinner');
            if (spinner) {
                spinner.remove();
            }
        },
        
        showError: function(message) {
            if (window.ui && window.ui.showError) {
                return window.ui.showError(message);
            }
            
            // Fallback implementation
            console.error(message);
            alert(message);
        },
        
        showMessage: function(message) {
            if (window.ui && window.ui.showMessage) {
                return window.ui.showMessage(message);
            }
            
            // Fallback implementation
            console.log(message);
            alert(message);
        }
    };
    
    // Fallback API utilities
    const apiUtils = {
        getResearchHistory: async function() {
            if (window.api && window.api.getResearchHistory) {
                return window.api.getResearchHistory();
            }
            
            // Fallback implementation
            try {
                const response = await fetch('/research/api/history');
                if (!response.ok) {
                    throw new Error(`API Error: ${response.status} ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        },
        
        deleteResearch: async function(researchId) {
            if (window.api && window.api.deleteResearch) {
                return window.api.deleteResearch(researchId);
            }
            
            // Fallback implementation
            try {
                const response = await fetch(`/research/api/delete/${researchId}`, {
                    method: 'DELETE'
                });
                if (!response.ok) {
                    throw new Error(`API Error: ${response.status} ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        },
        
        clearResearchHistory: async function() {
            if (window.api && window.api.clearResearchHistory) {
                return window.api.clearResearchHistory();
            }
            
            // Fallback implementation
            try {
                const response = await fetch('/research/api/clear_history', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                if (!response.ok) {
                    throw new Error(`API Error: ${response.status} ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        }
    };
    
    /**
     * Initialize the history component
     */
    function initializeHistory() {
        // Get DOM elements
        historyContainer = document.getElementById('history-items');
        searchInput = document.getElementById('history-search');
        clearHistoryBtn = document.getElementById('clear-history-btn');
        historyEmptyMessage = document.getElementById('history-empty-message');
        
        if (!historyContainer) {
            console.error('Required DOM elements not found for history component');
            return;
        }
        
        // Set up event listeners
        setupEventListeners();
        
        // Load history data
        loadHistoryData();
        
        console.log('History component initialized');
    }
    
    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Search input
        if (searchInput) {
            searchInput.addEventListener('input', handleSearchInput);
        }
        
        // Clear history button
        if (clearHistoryBtn) {
            clearHistoryBtn.addEventListener('click', handleClearHistory);
        }
        
        // Delegation for history item clicks
        if (historyContainer) {
            historyContainer.addEventListener('click', function(e) {
                // Handle delete button click
                if (e.target && e.target.closest('.delete-item-btn')) {
                    const itemId = e.target.closest('.history-item').dataset.id;
                    handleDeleteItem(itemId);
                }
            });
        }
    }
    
    /**
     * Load history data from API
     */
    async function loadHistoryData() {
        // Show loading state
        uiUtils.showSpinner(historyContainer, 'Loading research history...');
        
        try {
            // Get history items
            const response = await apiUtils.getResearchHistory();
            
            if (response && Array.isArray(response.items)) {
                historyItems = response.items;
                filteredItems = [...historyItems];
                
                // Render history items
                renderHistoryItems();
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('Error loading history:', error);
            uiUtils.hideSpinner(historyContainer);
            uiUtils.showError('Error loading history: ' + error.message);
        }
    }
    
    /**
     * Render history items
     */
    function renderHistoryItems() {
        // Hide spinner
        uiUtils.hideSpinner(historyContainer);
        
        // Clear container
        historyContainer.innerHTML = '';
        
        // Show empty message if no items
        if (filteredItems.length === 0) {
            if (historyEmptyMessage) {
                historyEmptyMessage.style.display = 'block';
            } else {
                historyContainer.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-history empty-icon"></i>
                        <p>No research history found.</p>
                        ${searchInput && searchInput.value ? '<p>Try adjusting your search query.</p>' : ''}
                    </div>
                `;
            }
            
            if (clearHistoryBtn) {
                clearHistoryBtn.style.display = 'none';
            }
            return;
        }
        
        // Hide empty message
        if (historyEmptyMessage) {
            historyEmptyMessage.style.display = 'none';
        }
        
        // Show clear button
        if (clearHistoryBtn) {
            clearHistoryBtn.style.display = 'inline-block';
        }
        
        // Create items
        filteredItems.forEach(item => {
            const itemElement = createHistoryItemElement(item);
            historyContainer.appendChild(itemElement);
        });
    }
    
    /**
     * Format date safely using the formatter if available
     */
    function formatDate(dateStr) {
        if (window.formatting && window.formatting.formatDate) {
            return window.formatting.formatDate(dateStr);
        }
        
        // Simple fallback date formatting
        try {
            const date = new Date(dateStr);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        } catch (e) {
            return dateStr;
        }
    }
    
    /**
     * Format status safely using the formatter if available
     */
    function formatStatus(status) {
        if (window.formatting && window.formatting.formatStatus) {
            return window.formatting.formatStatus(status);
        }
        
        // Simple fallback formatting
        const statusMap = {
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'failed': 'Failed',
            'suspended': 'Suspended'
        };
        
        return statusMap[status] || status;
    }
    
    /**
     * Format mode safely using the formatter if available
     */
    function formatMode(mode) {
        if (window.formatting && window.formatting.formatMode) {
            return window.formatting.formatMode(mode);
        }
        
        // Simple fallback formatting
        const modeMap = {
            'quick': 'Quick Summary',
            'detailed': 'Detailed Report'
        };
        
        return modeMap[mode] || mode;
    }
    
    /**
     * Create a history item element
     * @param {Object} item - The history item data
     * @returns {HTMLElement} The history item element
     */
    function createHistoryItemElement(item) {
        const itemEl = document.createElement('div');
        itemEl.className = 'history-item';
        itemEl.dataset.id = item.id;
        
        // Format date
        const formattedDate = formatDate(item.created_at);
        
        // Get a display title (use query if title is not available)
        const displayTitle = item.title || formatTitleFromQuery(item.query);
        
        // Status class - convert in_progress to in-progress for CSS
        const statusClass = item.status ? item.status.replace('_', '-') : '';
        
        // Create the HTML content
        itemEl.innerHTML = `
            <div class="history-item-header">
                <div class="history-item-title">${displayTitle}</div>
                <div class="history-item-status status-${statusClass}">${formatStatus(item.status)}</div>
            </div>
            <div class="history-item-meta">
                <div class="history-item-date">${formattedDate}</div>
                <div class="history-item-mode">${formatMode(item.mode)}</div>
            </div>
            <div class="history-item-actions">
                ${item.status === 'completed' ? 
                    `<button class="btn btn-sm btn-outline view-btn">
                        <i class="fas fa-eye"></i> View
                    </button>` : ''}
                <button class="btn btn-sm btn-outline delete-item-btn">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        `;
        
        // Add event listeners
        const viewBtn = itemEl.querySelector('.view-btn');
        if (viewBtn) {
            viewBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent item click
                window.location.href = `/research/results/${item.id}`;
            });
        }
        
        const deleteBtn = itemEl.querySelector('.delete-item-btn');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent item click
                handleDeleteItem(item.id);
            });
        }
        
        // Add click event to the whole item
        itemEl.addEventListener('click', () => {
            if (item.status === 'completed') {
                window.location.href = `/research/results/${item.id}`;
            } else {
                window.location.href = `/research/progress/${item.id}`;
            }
        });
        
        return itemEl;
    }
    
    /**
     * Format a title from a query string
     * Truncates long queries and adds ellipsis
     * @param {string} query - The query string
     * @returns {string} Formatted title
     */
    function formatTitleFromQuery(query) {
        if (!query) return 'Untitled Research';
        
        // Truncate long queries
        if (query.length > 60) {
            return query.substring(0, 57) + '...';
        }
        
        return query;
    }
    
    /**
     * Handle search input
     */
    function handleSearchInput() {
        const searchTerm = searchInput.value.trim().toLowerCase();
        
        if (!searchTerm) {
            // Reset to show all items
            filteredItems = [...historyItems];
        } else {
            // Filter items based on search term
            filteredItems = historyItems.filter(item => {
                // Search in title if available, otherwise in query
                const titleMatch = item.title ? 
                    item.title.toLowerCase().includes(searchTerm) : 
                    false;
                    
                // Always search in query
                const queryMatch = item.query ? 
                    item.query.toLowerCase().includes(searchTerm) : 
                    false;
                    
                return titleMatch || queryMatch;
            });
        }
        
        // Render filtered items
        renderHistoryItems();
    }
    
    /**
     * Handle delete item
     * @param {string} itemId - The item ID to delete
     */
    async function handleDeleteItem(itemId) {
        if (!confirm('Are you sure you want to delete this research? This action cannot be undone.')) {
            return;
        }
        
        try {
            // Delete item via API
            await apiUtils.deleteResearch(itemId);
            
            // Remove from arrays
            historyItems = historyItems.filter(item => item.id != itemId);
            filteredItems = filteredItems.filter(item => item.id != itemId);
            
            // Show success message
            uiUtils.showMessage('Research deleted successfully');
            
            // Re-render history items
            renderHistoryItems();
        } catch (error) {
            console.error('Error deleting research:', error);
            uiUtils.showError('Error deleting research: ' + error.message);
        }
    }
    
    /**
     * Handle clear history
     */
    async function handleClearHistory() {
        if (!confirm('Are you sure you want to clear all research history? This action cannot be undone.')) {
            return;
        }
        
        try {
            // Clear history via API
            await apiUtils.clearResearchHistory();
            
            // Clear arrays
            historyItems = [];
            filteredItems = [];
            
            // Show success message
            uiUtils.showMessage('Research history cleared successfully');
            
            // Re-render history items
            renderHistoryItems();
        } catch (error) {
            console.error('Error clearing history:', error);
            uiUtils.showError('Error clearing history: ' + error.message);
        }
    }
    
    // Initialize on DOM content loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeHistory);
    } else {
        initializeHistory();
    }
})(); 