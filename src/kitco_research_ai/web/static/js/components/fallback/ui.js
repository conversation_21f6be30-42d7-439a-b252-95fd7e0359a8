/**
 * UI Fallback Utilities
 * Basic implementations of UI utilities that can be used if the main UI module is not available
 */
(function() {
    // Only initialize if window.ui is not already defined
    if (window.ui) {
        console.log('Main UI utilities already available, skipping fallback');
        return;
    }

    console.log('Initializing fallback UI utilities');

    /**
     * Show a loading spinner
     * @param {HTMLElement} container - Container element for spinner
     * @param {string} message - Optional loading message
     */
    function showSpinner(container, message) {
        if (!container) container = document.body;
        const spinnerHtml = `
            <div class="loading-spinner centered">
                <div class="spinner"></div>
                ${message ? `<div class="spinner-message">${message}</div>` : ''}
            </div>
        `;
        container.innerHTML = spinnerHtml;
    }

    /**
     * Hide loading spinner
     * @param {HTMLElement} container - Container with spinner
     */
    function hideSpinner(container) {
        if (!container) container = document.body;
        const spinner = container.querySelector('.loading-spinner');
        if (spinner) {
            spinner.remove();
        }
    }

    /**
     * Show an error message
     * @param {string} message - Error message to display
     */
    function showError(message) {
        console.error(message);
        
        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;
        
        // Add to the page if a notification container exists, otherwise use alert
        const container = document.querySelector('.notifications-container');
        if (container) {
            container.appendChild(notification);
            
            // Remove after a delay
            setTimeout(() => {
                notification.classList.add('removing');
                setTimeout(() => notification.remove(), 500);
            }, 5000);
            
            // Set up close button
            const closeBtn = notification.querySelector('.close-notification');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    notification.classList.add('removing');
                    setTimeout(() => notification.remove(), 500);
                });
            }
        } else {
            // Fallback to alert - only as last resort
            console.warn('No notification container found, using alert as fallback');
            alert(message);
        }
    }

    /**
     * Show a success/info message
     * @param {string} message - Message to display
     */
    function showMessage(message) {
        console.log(message);
        
        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
            <button class="close-notification"><i class="fas fa-times"></i></button>
        `;
        
        // Add to the page if a notification container exists, otherwise use alert
        const container = document.querySelector('.notifications-container');
        if (container) {
            container.appendChild(notification);
            
            // Remove after a delay
            setTimeout(() => {
                notification.classList.add('removing');
                setTimeout(() => notification.remove(), 500);
            }, 5000);
            
            // Set up close button
            const closeBtn = notification.querySelector('.close-notification');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    notification.classList.add('removing');
                    setTimeout(() => notification.remove(), 500);
                });
            }
        } else {
            // Fallback to alert - only as last resort
            console.warn('No notification container found, using alert as fallback');
            alert(message);
        }
    }

    /**
     * Simple markdown renderer
     * @param {string} markdown - Markdown content
     * @returns {string} HTML content
     */
    function renderMarkdown(markdown) {
        if (!markdown) return '';
        
        // This is a very basic markdown renderer for fallback purposes
        let html = markdown;
        
        // Convert headers
        html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
        html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
        html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
        html = html.replace(/^##### (.*$)/gm, '<h5>$1</h5>');
        
        // Convert code blocks
        html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
        
        // Convert inline code
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Convert bold
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Convert italic
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Convert links
        html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
        
        // Convert paragraphs - this is simplistic
        html = html.replace(/\n\s*\n/g, '</p><p>');
        html = '<p>' + html + '</p>';
        
        // Fix potentially broken paragraph tags
        html = html.replace(/<\/p><p><\/p><p>/g, '</p><p>');
        html = html.replace(/<\/p><p><(h[1-5])/g, '</p><$1');
        html = html.replace(/<\/(h[1-5])><p>/g, '</$1>');
        
        return html;
    }

    /**
     * Update favicon to indicate status
     * @param {string} status - Status to indicate (active, complete, error)
     */
    function updateFavicon(status) {
        try {
            const faviconLink = document.querySelector('link[rel="icon"]') || 
                document.querySelector('link[rel="shortcut icon"]');
            
            if (!faviconLink) {
                console.warn('Favicon link not found');
                return;
            }
            
            let iconPath;
            switch (status) {
                case 'active':
                    iconPath = '/research/static/img/favicon-active.ico';
                    break;
                case 'complete':
                    iconPath = '/research/static/img/favicon-complete.ico';
                    break;
                case 'error':
                    iconPath = '/research/static/img/favicon-error.ico';
                    break;
                default:
                    iconPath = '/research/static/img/favicon.ico';
            }
            
            // Add cache busting parameter to force reload
            faviconLink.href = iconPath + '?v=' + new Date().getTime();
            console.log('Updated favicon to:', status);
        } catch (error) {
            console.error('Failed to update favicon:', error);
        }
    }

    // Export utilities to window.ui
    window.ui = {
        showSpinner,
        hideSpinner,
        showError,
        showMessage,
        renderMarkdown,
        updateFavicon
    };
})(); 