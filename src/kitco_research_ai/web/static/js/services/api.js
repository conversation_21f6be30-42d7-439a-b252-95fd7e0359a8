/**
 * API Service
 * Handles all communication with the server API endpoints
 */

// Base URL for API - declare globally
let API_BASE_URL;
if (typeof API_BASE_URL === 'undefined') {
    API_BASE_URL = '/research/api';
}

/**
 * Get CSRF token from meta tag
 * @returns {string} The CSRF token
 */
function getCsrfToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : '';
}

/**
 * Get the full URL for an API endpoint
 * @param {string} path - The API path (without leading slash)
 * @returns {string} The full API URL
 */
function getApiUrl(path) {
    return `${API_BASE_URL}/${path}`;
}

/**
 * Generic fetch with error handling
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<any>} The parsed response data
 */
async function fetchWithErrorHandling(url, options = {}) {
    try {
        const csrfToken = getCsrfToken();
        const response = await fetch(url, {
            ...options,
            headers: {
                'Content-Type': 'application/json',
                ...(csrfToken ? { 'X-CSRFToken': csrfToken } : {}),
                ...(options.headers || {})
            }
        });

        // Handle non-200 responses
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || `API Error: ${response.status} ${response.statusText}`);
        }

        // Parse the response
        return await response.json();
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

/**
 * Helper method to perform POST requests with JSON data
 * @param {string} path - API path
 * @param {Object} data - JSON data to send
 * @returns {Promise<any>} Response data
 */
async function postJSON(path, data) {
    return fetchWithErrorHandling(path, {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

/**
 * Start a new research
 * @param {string} query - The research query
 * @param {string} mode - The research mode (quick/detailed)
 * @returns {Promise<Object>} The research response with ID
 */
async function startResearch(query, mode) {
    return postJSON('/research/api/start_research', { query, mode });
}

/**
 * Get a research status by ID
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The research status
 */
async function getResearchStatus(researchId) {
    return fetchWithErrorHandling(`/research/api/status/${researchId}`);
}

/**
 * Get research details
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The research details
 */
async function getResearchDetails(researchId) {
    return fetchWithErrorHandling(`/research/api/details/${researchId}`);
}

/**
 * Get research logs
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The research logs
 */
async function getResearchLogs(researchId) {
    return fetchWithErrorHandling(`/research/api/logs/${researchId}`);
}

/**
 * Get research history
 * @returns {Promise<Array>} The research history
 */
async function getResearchHistory() {
    return fetchWithErrorHandling('/research/api/history');
}

/**
 * Get research report
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The research report
 */
async function getReport(researchId) {
    return fetchWithErrorHandling(`/research/api/report/${researchId}`);
}

/**
 * Terminate a research
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The termination response
 */
async function terminateResearch(researchId) {
    return postJSON(`/research/api/terminate/${researchId}`, {});
}

/**
 * Delete a research
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The deletion response
 */
async function deleteResearch(researchId) {
    const csrfToken = getCsrfToken();
    return fetchWithErrorHandling(`/research/api/delete/${researchId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': csrfToken
        }
    });
}

/**
 * Clear all research history
 * @returns {Promise<Object>} The response
 */
async function clearResearchHistory() {
    return postJSON('/research/api/clear_history', {});
}

/**
 * Open a file location in the system file explorer
 * @param {string} path - Path to open
 * @returns {Promise<Object>} The response
 */
async function openFileLocation(path) {
    return postJSON('/research/open_file_location', { path });
}

/**
 * Save raw configuration
 * @param {string} rawConfig - Raw configuration text
 * @returns {Promise<Object>} The response
 */
async function saveRawConfig(rawConfig) {
    return postJSON('/research/api/save_raw_config', { raw_config: rawConfig });
}

/**
 * Save main configuration
 * @param {Object} config - Configuration object
 * @returns {Promise<Object>} The response
 */
async function saveMainConfig(config) {
    return postJSON('/research/api/save_main_config', config);
}

/**
 * Save search engines configuration
 * @param {Object} config - Configuration object
 * @returns {Promise<Object>} The response
 */
async function saveSearchEnginesConfig(config) {
    return postJSON('/research/api/save_search_engines_config', config);
}

/**
 * Save collections configuration
 * @param {Object} config - Configuration object
 * @returns {Promise<Object>} The response
 */
async function saveCollectionsConfig(config) {
    return postJSON('/research/api/save_collections_config', config);
}

/**
 * Save API keys configuration
 * @param {Object} config - Configuration object
 * @returns {Promise<Object>} The response
 */
async function saveApiKeysConfig(config) {
    return postJSON('/research/api/save_api_keys_config', config);
}

/**
 * Save LLM configuration
 * @param {Object} config - Configuration object
 * @returns {Promise<Object>} The response
 */
async function saveLlmConfig(config) {
    return postJSON('/research/api/save_llm_config', config);
}

/**
 * Get markdown export for research
 * @param {number} researchId - The research ID
 * @returns {Promise<Object>} The markdown content
 */
async function getMarkdownExport(researchId) {
    return fetchWithErrorHandling(`/research/api/markdown/${researchId}`);
}

// Export the API functions
window.api = {
    startResearch,
    getResearchStatus,
    getResearchDetails,
    getResearchLogs,
    getResearchHistory,
    getReport,
    getMarkdownExport,
    terminateResearch,
    deleteResearch,
    clearResearchHistory,
    openFileLocation,
    saveRawConfig,
    saveMainConfig,
    saveSearchEnginesConfig,
    saveCollectionsConfig,
    saveApiKeysConfig,
    saveLlmConfig,
    postJSON,
    getApiUrl
};
