/**
 * HTML Sanitization Service
 * Provides lightweight HTML sanitization to prevent XSS attacks
 */

/**
 * Basic HTML sanitizer - allows only safe tags and attributes
 * @param {string} html - The HTML string to sanitize
 * @returns {string} The sanitized HTML string
 */
function sanitizeHtml(html) {
    if (!html || typeof html !== 'string') {
        return '';
    }

    // Allowed tags and their allowed attributes
    const allowedTags = {
        'p': [],
        'br': [],
        'strong': [],
        'b': [],
        'em': [],
        'i': [],
        'u': [],
        'h1': [],
        'h2': [],
        'h3': [],
        'h4': [],
        'h5': [],
        'h6': [],
        'ul': [],
        'ol': [],
        'li': [],
        'blockquote': [],
        'code': ['class'],
        'pre': ['class'],
        'a': ['href', 'title', 'target'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
        'div': ['class'],
        'span': ['class'],
        'table': ['class'],
        'thead': [],
        'tbody': [],
        'tr': [],
        'th': [],
        'td': []
    };

    // Create a temporary DOM element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Recursively sanitize elements
    function sanitizeElement(element) {
        const tagName = element.tagName.toLowerCase();
        
        // If tag is not allowed, replace with its text content
        if (!allowedTags.hasOwnProperty(tagName)) {
            const textNode = document.createTextNode(element.textContent || '');
            element.parentNode.replaceChild(textNode, element);
            return;
        }

        // Remove disallowed attributes
        const allowedAttrs = allowedTags[tagName];
        const attrs = Array.from(element.attributes);
        
        attrs.forEach(attr => {
            if (!allowedAttrs.includes(attr.name)) {
                element.removeAttribute(attr.name);
            } else {
                // Sanitize attribute values
                const value = attr.value;
                
                // For href attributes, only allow safe protocols
                if (attr.name === 'href') {
                    if (!value.match(/^(https?:\/\/|mailto:|#)/i)) {
                        element.removeAttribute(attr.name);
                    }
                }
                
                // For src attributes, only allow safe protocols
                if (attr.name === 'src') {
                    if (!value.match(/^(https?:\/\/|data:image\/)/i)) {
                        element.removeAttribute(attr.name);
                    }
                }
                
                // Remove javascript: and other dangerous protocols
                if (value.match(/javascript:|vbscript:|data:(?!image\/)/i)) {
                    element.removeAttribute(attr.name);
                }
            }
        });

        // Recursively sanitize child elements
        const children = Array.from(element.children);
        children.forEach(child => sanitizeElement(child));
    }

    // Sanitize all elements
    const children = Array.from(tempDiv.children);
    children.forEach(child => sanitizeElement(child));

    return tempDiv.innerHTML;
}

/**
 * Sanitize text for safe insertion into HTML
 * @param {string} text - The text to sanitize
 * @returns {string} The sanitized text
 */
function sanitizeText(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    return text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;');
}

/**
 * Create safe HTML with highlighted text
 * @param {string} text - The text to highlight
 * @param {string} search - The search term to highlight
 * @param {string} className - CSS class for highlighting (default: 'highlight')
 * @returns {string} Safe HTML with highlighted text
 */
function createHighlightedText(text, search, className = 'highlight') {
    if (!text || !search || typeof text !== 'string' || typeof search !== 'string') {
        return sanitizeText(text || '');
    }

    // Sanitize inputs
    const safeText = sanitizeText(text);
    const safeSearch = search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const safeClassName = className.replace(/[^a-zA-Z0-9_-]/g, '');

    // Create regex for case-insensitive search
    const regex = new RegExp(`(${safeSearch})`, 'gi');
    
    // Replace matches with highlighted spans
    return safeText.replace(regex, `<span class="${safeClassName}">$1</span>`);
}

/**
 * Strip all HTML tags from a string
 * @param {string} html - The HTML string
 * @returns {string} Plain text without HTML tags
 */
function stripHtml(html) {
    if (!html || typeof html !== 'string') {
        return '';
    }

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
}

// Export the sanitization functions
window.sanitizer = {
    sanitizeHtml,
    sanitizeText,
    createHighlightedText,
    stripHtml
};
