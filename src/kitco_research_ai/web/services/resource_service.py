import json
import logging
from datetime import datetime

from ..models.database import get_db_connection

logger = logging.getLogger(__name__)


def get_resources_for_research(research_id):
    """
    Retrieve resources associated with a specific research project

    Args:
        research_id (int): The ID of the research

    Returns:
        list: List of resource objects for the research
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Query to get resources for the research
        cursor.execute(
            "SELECT id, research_id, title, url, content_preview, source_type, metadata "
            "FROM research_resources WHERE research_id = ? ORDER BY id ASC",
            (research_id,),
        )

        resources = []
        for row in cursor.fetchall():
            id, research_id, title, url, content_preview, source_type, metadata_str = (
                row
            )

            # Parse metadata if available
            metadata = {}
            if metadata_str:
                try:
                    metadata = json.loads(metadata_str)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in metadata for resource {id}")

            resources.append(
                {
                    "id": id,
                    "research_id": research_id,
                    "title": title,
                    "url": url,
                    "content_preview": content_preview,
                    "source_type": source_type,
                    "metadata": metadata,
                }
            )

        conn.close()
        return resources

    except Exception as e:
        logger.error(f"Error retrieving resources for research {research_id}: {str(e)}")
        raise


def add_resource(
    research_id, title, url, content_preview=None, source_type="web", metadata=None
):
    """
    Add a new resource to the research_resources table

    Args:
        research_id (int): The ID of the research
        title (str): The title of the resource
        url (str): The URL of the resource
        content_preview (str, optional): A preview or snippet of the resource content
        source_type (str, optional): The type of resource (web, pdf, etc.)
        metadata (dict, optional): Additional metadata for the resource

    Returns:
        int: The ID of the newly created resource
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        created_at = datetime.utcnow().isoformat()
        metadata_json = json.dumps(metadata) if metadata else None

        cursor.execute(
            "INSERT INTO research_resources (research_id, title, url, content_preview, source_type, metadata, created_at) "
            "VALUES (?, ?, ?, ?, ?, ?, ?)",
            (
                research_id,
                title,
                url,
                content_preview,
                source_type,
                metadata_json,
                created_at,
            ),
        )

        resource_id = cursor.lastrowid
        conn.commit()
        conn.close()

        logger.info(f"Added resource {resource_id} for research {research_id}: {title}")
        return resource_id

    except Exception as e:
        logger.error(f"Error adding resource for research {research_id}: {str(e)}")
        raise


def delete_resource(resource_id):
    """
    Delete a resource from the database

    Args:
        resource_id (int): The ID of the resource to delete

    Returns:
        bool: True if the resource was deleted successfully, False otherwise
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # First check if the resource exists
        cursor.execute("SELECT id FROM research_resources WHERE id = ?", (resource_id,))
        result = cursor.fetchone()

        if not result:
            conn.close()
            return False

        # Delete the resource
        cursor.execute("DELETE FROM research_resources WHERE id = ?", (resource_id,))

        rows_affected = cursor.rowcount
        conn.commit()
        conn.close()

        logger.info(f"Deleted resource {resource_id}, {rows_affected} rows affected")
        return rows_affected > 0

    except Exception as e:
        logger.error(f"Error deleting resource {resource_id}: {str(e)}")
        raise
