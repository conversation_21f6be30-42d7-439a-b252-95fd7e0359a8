from loguru import logger
from sqlalchemy import inspect

from ..services.settings_manager import SettingsManager
from .models import Base, Setting


def import_default_settings_file(db_session):
    """
    Imports all settings from the default settings file to the DB.
    """
    settings_mgr = SettingsManager(db_session)
    if settings_mgr.db_version_matches_package():
        # We probably shouldn't bother loading settings if the version didn't
        # change.
        return
    logger.info("Detected a new version of default settings, upgrading DB.")

    # Create settings manager and import settings
    try:
        # This will not overwrite existing settings, but will delete
        # extraneous ones. This should be enough to update anyone with
        # old versions of the settings.
        settings_mgr.load_from_defaults_file(overwrite=False, delete_extra=True)
        logger.info("Successfully imported settings from files")


        # Update the saved version.
        settings_mgr.update_db_version()
    except Exception as e:
        logger.error("Error importing settings from files: %s", e)



def run_migrations(engine, db_session=None):
    """
    Run any necessary database migrations

    Args:
        engine: SQLAlchemy engine
        db_session: Optional SQLAlchemy session
    """
    # Create all tables if they don't exist
    inspector = inspect(engine)
    if not inspector.has_table("settings"):
        logger.info("Creating settings table")
        Base.metadata.create_all(engine, tables=[Setting.__table__])

    # Import existing settings from files
    if db_session:
        import_default_settings_file(db_session)
