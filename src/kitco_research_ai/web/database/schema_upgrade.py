"""
Schema upgrade script for Kitco Research AI database.
Handles schema upgrades for existing ldr.db databases.
"""

import os
import sqlite3
import sys

from loguru import logger

# Add the parent directory to sys.path to allow relative imports
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
)

try:
    from src.kitco_research_ai.web.models.database import DB_PATH
except ImportError:
    # Fallback path if import fails
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, "..", "..", "..", ".."))
    DB_PATH = os.path.join(project_root, "data", "ldr.db")


def check_table_exists(conn, table_name):
    """
    Check if a table exists in the database

    Args:
        conn: SQLite connection
        table_name: Name of the table

    Returns:
        bool: True if table exists, False otherwise
    """
    cursor = conn.cursor()
    cursor.execute(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,)
    )
    return cursor.fetchone() is not None


def remove_research_log_table(conn):
    """
    Remove the redundant research_log table if it exists

    Args:
        conn: SQLite connection

    Returns:
        bool: True if operation was successful, False otherwise
    """
    try:
        cursor = conn.cursor()

        # Check if table exists
        if check_table_exists(conn, "research_log"):
            # For SQLite, DROP TABLE is the way to remove a table
            cursor.execute("DROP TABLE research_log")
            conn.commit()
            logger.info("Successfully removed redundant 'research_log' table")
            return True
        else:
            logger.info("Table 'research_log' does not exist, no action needed")
            return True
    except Exception:
        logger.exception("Error removing research_log table")
        return False


def remove_deprecated_llm_providers(conn):
    """
    Remove deprecated LLM provider settings (Anthropic and Llamacpp)

    Args:
        conn: SQLite connection

    Returns:
        bool: True if operation was successful, False otherwise
    """
    try:
        cursor = conn.cursor()

        # Check if settings table exists first
        if not check_table_exists(conn, "settings"):
            logger.info("Settings table does not exist, skipping deprecated LLM provider cleanup")
            return True

        # List of deprecated settings to remove
        deprecated_settings = [
            'llm.anthropic.api_key',
            'llm.llamacpp_f16_kv',
            'llm.llamacpp_model_path',
            'llm.llamacpp_n_batch',
            'llm.llamacpp_n_gpu_layers',
            'llm.llamacpp_connection_mode',
            'llm.llamacpp_server_url'
        ]

        removed_count = 0
        for setting_key in deprecated_settings:
            cursor.execute("DELETE FROM settings WHERE key = ?", (setting_key,))
            if cursor.rowcount > 0:
                removed_count += 1
                logger.info(f"Removed deprecated setting: {setting_key}")

        if removed_count > 0:
            conn.commit()
            logger.info(f"Successfully removed {removed_count} deprecated LLM provider settings")
        else:
            logger.info("No deprecated LLM provider settings found to remove")

        return True
    except Exception:
        logger.exception("Error removing deprecated LLM provider settings")
        return False


def force_settings_reload(conn):
    """
    Force settings reload by updating the app version to trigger migration

    Args:
        conn: SQLite connection

    Returns:
        bool: True if operation was successful, False otherwise
    """
    try:
        cursor = conn.cursor()

        # Check if settings table exists first
        if not check_table_exists(conn, "settings"):
            logger.info("Settings table does not exist, skipping app version update")
            return True

        # Update the app version to force settings reload (use JSON format)
        cursor.execute("UPDATE settings SET value = '\"0.4.1\"' WHERE key = 'application.technical_settings.version'")
        conn.commit()
        logger.info("Updated app version to force settings reload")
        return True
    except Exception:
        logger.exception("Error updating app version")
        return False


def run_schema_upgrades():
    """
    Run all schema upgrade operations on the database

    Returns:
        bool: True if all upgrades successful, False otherwise
    """
    # Check if database exists
    if not os.path.exists(DB_PATH):
        logger.warning(f"Database not found at {DB_PATH}, skipping schema upgrades")
        return False

    logger.info(f"Running schema upgrades on {DB_PATH}")

    try:
        # Connect to the database
        conn = sqlite3.connect(DB_PATH)

        # 1. Remove the redundant research_log table
        remove_research_log_table(conn)

        # 2. Remove deprecated LLM provider settings
        remove_deprecated_llm_providers(conn)

        # 3. Force settings reload to clean up any remaining deprecated settings
        force_settings_reload(conn)

        # Close connection
        conn.close()

        logger.info("Schema upgrades completed successfully")
        return True
    except Exception:
        logger.exception("Error during schema upgrades")
        return False


if __name__ == "__main__":
    run_schema_upgrades()
