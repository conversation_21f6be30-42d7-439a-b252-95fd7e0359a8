# Database Architecture

## Overview

Kitco Research AI now uses a unified database architecture with a single SQLite database file (`ldr.db`) that replaces the previous split database approach (`deep_research.db` and `research_history.db`).

The database is located at `data/ldr.db` within the project directory structure.

## Database-First Settings

The application now follows a "database-first" approach for settings:

1. All settings are stored in the database, in the `settings` table
2. Settings from TOML files are used only as fallbacks if a setting doesn't exist in the database
3. The web UI settings page modifies the database values directly

## Migration

If you have existing data in the legacy databases, you need to migrate it to the new unified database.

### Database Initialization

The application automatically initializes the database on first startup:

1. If `ldr.db` doesn't exist, it will be created automatically
2. All required tables and schema will be set up
3. Default settings will be imported from configuration files

### Schema Upgrades

The application automatically handles schema upgrades:

1. Schema upgrades run automatically on startup when needed
2. You can manually run schema upgrades with: `python -m kitco_research_ai.web.database.schema_upgrade`
3. Current schema upgrades include:
   - Removing the redundant `research_log` table (consolidated into `research_logs`)
   - Cleaning up deprecated LLM provider settings

## Database Schema

The unified database contains:

* `research_history` - Research history entries (from research_history.db)
* `research_logs` - Consolidated logs for all research activities (merged from research_history.db)
* `research_resources` - Resources found during research (from research_history.db)
* `settings` - Application settings (from deep_research.db)
* `research` - Research data (from deep_research.db)
* `research_report` - Generated research reports (from deep_research.db)

## Rollback

If you need to roll back to the previous database architecture:

1. Keep backup copies of your original `deep_research.db` and `research_history.db` files
2. In case of issues, restore them and modify the database paths in the code

## Troubleshooting

If you encounter issues with database migration:

1. Check the application logs for detailed error messages
2. Ensure you have write permissions to the data directory
3. Make sure SQLite is functioning properly
4. If necessary, start with a fresh database by removing `ldr.db`
