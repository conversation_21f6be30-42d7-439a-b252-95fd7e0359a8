"""
Database service implementation.

This service provides a clean interface to database operations
with proper error handling and connection management.
"""

import os
import sqlite3
import threading
from typing import Any, Dict, Optional

from loguru import logger
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .interfaces import IDatabaseService, IConfigService
from ..core.exceptions import DatabaseError
from ..web.database.models import Base


class DatabaseService(IDatabaseService):
    """
    Database service that manages database connections and operations.
    
    This service provides a clean interface to database operations
    with proper error handling, connection pooling, and health monitoring.
    """
    
    def __init__(self, config_service: IConfigService):
        self.config_service = config_service
        self.engine = None
        self.session_factory = None
        self._lock = threading.RLock()
        self._initialized = False
        logger.info("Database service created")
    
    def initialize(self) -> None:
        """
        Initialize the database service.
        
        Raises:
            DatabaseError: If initialization fails
        """
        if self._initialized:
            return
        
        with self._lock:
            if self._initialized:
                return
            
            try:
                logger.info("Initializing database service")
                
                # Get database configuration
                db_config = self.config_service.get_database_config()
                db_path = db_config["path"]
                
                # Ensure database directory exists
                os.makedirs(os.path.dirname(db_path), exist_ok=True)
                
                # Create engine
                database_url = f"sqlite:///{db_path}"
                self.engine = create_engine(
                    database_url,
                    echo=db_config.get("echo", False),
                    pool_size=db_config.get("pool_size", 5),
                    max_overflow=db_config.get("max_overflow", 10),
                    pool_pre_ping=True
                )
                
                # Create session factory
                self.session_factory = sessionmaker(bind=self.engine)
                
                # Create tables if they don't exist
                self._create_tables()
                
                # Run any pending migrations
                self._run_migrations()
                
                self._initialized = True
                logger.info(f"Database service initialized with database at {db_path}")
                
            except Exception as e:
                logger.error(f"Failed to initialize database service: {e}")
                raise DatabaseError(
                    f"Database initialization failed: {e}",
                    error_code="DB_INIT_FAILED",
                    details={"original_error": str(e)}
                )
    
    def get_connection(self):
        """
        Get a database connection.
        
        Returns:
            Database connection
            
        Raises:
            DatabaseError: If connection fails
        """
        if not self._initialized:
            raise DatabaseError(
                "Database service not initialized",
                error_code="DB_NOT_INITIALIZED"
            )
        
        try:
            return self.engine.connect()
        except Exception as e:
            logger.error(f"Failed to get database connection: {e}")
            raise DatabaseError(
                f"Failed to get database connection: {e}",
                error_code="DB_CONNECTION_FAILED",
                details={"original_error": str(e)}
            )
    
    def get_session(self) -> Session:
        """
        Get a database session.
        
        Returns:
            SQLAlchemy session
            
        Raises:
            DatabaseError: If session creation fails
        """
        if not self._initialized:
            raise DatabaseError(
                "Database service not initialized",
                error_code="DB_NOT_INITIALIZED"
            )
        
        try:
            return self.session_factory()
        except Exception as e:
            logger.error(f"Failed to create database session: {e}")
            raise DatabaseError(
                f"Failed to create database session: {e}",
                error_code="DB_SESSION_FAILED",
                details={"original_error": str(e)}
            )
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> Any:
        """
        Execute a database query.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            Query result
            
        Raises:
            DatabaseError: If query execution fails
        """
        if not self._initialized:
            raise DatabaseError(
                "Database service not initialized",
                error_code="DB_NOT_INITIALIZED"
            )
        
        try:
            with self.get_connection() as conn:
                # Begin transaction for write operations
                with conn.begin():
                    result = conn.execute(text(query), params or {})

                    # Only fetch results if the statement returns rows
                    if result.returns_rows:
                        return result.fetchall()
                    else:
                        # For INSERT/UPDATE/DELETE operations, return affected row count
                        return result.rowcount
        except SQLAlchemyError as e:
            logger.error(f"Database query failed: {e}")
            raise DatabaseError(
                f"Query execution failed: {e}",
                error_code="DB_QUERY_FAILED",
                details={"query": query, "params": params}
            )
    
    def health_check(self) -> bool:
        """
        Check database health.
        
        Returns:
            True if database is healthy
        """
        if not self._initialized:
            return False
        
        try:
            with self.get_connection() as conn:
                conn.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.warning(f"Database health check failed: {e}")
            return False
    
    def shutdown(self) -> None:
        """Shutdown database connections."""
        if not self._initialized:
            return
        
        with self._lock:
            try:
                if self.engine:
                    self.engine.dispose()
                    logger.info("Database connections closed")
            except Exception as e:
                logger.error(f"Error during database shutdown: {e}")
            finally:
                self._initialized = False
    
    def _create_tables(self) -> None:
        """Create database tables if they don't exist."""
        try:
            Base.metadata.create_all(self.engine)
            logger.info("Database tables created/verified")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise DatabaseError(
                f"Table creation failed: {e}",
                error_code="DB_TABLE_CREATION_FAILED"
            )
    
    def _run_migrations(self) -> None:
        """Run any pending database migrations."""
        try:
            # Check if we need to run schema upgrades
            from ..web.database.schema_upgrade import run_schema_upgrades
            run_schema_upgrades()
            logger.info("Database migrations completed")
        except Exception as e:
            logger.warning(f"Database migration warning: {e}")
            # Don't fail initialization for migration issues
    
    def backup_database(self, backup_path: str) -> bool:
        """
        Create a backup of the database.
        
        Args:
            backup_path: Path for the backup file
            
        Returns:
            True if backup successful
        """
        if not self._initialized:
            return False
        
        try:
            db_config = self.config_service.get_database_config()
            source_path = db_config["path"]
            
            # Use SQLite backup API for consistent backup
            source_conn = sqlite3.connect(source_path)
            backup_conn = sqlite3.connect(backup_path)
            
            source_conn.backup(backup_conn)
            
            source_conn.close()
            backup_conn.close()
            
            logger.info(f"Database backed up to {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            return False
