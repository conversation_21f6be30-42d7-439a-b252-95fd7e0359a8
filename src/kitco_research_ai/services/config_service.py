"""
Configuration service implementation.

This service provides a clean interface to configuration management
with proper error handling and validation.
"""

import os
from typing import Any, Dict, List, Optional

from loguru import logger

from .interfaces import IConfigService
from ..core.exceptions import ConfigurationError


class ConfigService(IConfigService):
    """
    Configuration service that manages application configuration.
    
    This service provides centralized configuration management with support for
    multiple configuration sources and environment variable overrides.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration service.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_path = config_path
        self._config_cache: Dict[str, Any] = {}
        self._initialized = False
        
        # Initialize configuration
        self._load_configuration()
    
    def _load_configuration(self) -> None:
        """Load configuration from various sources."""
        try:
            # Import the actual config system
            import sys
            import os
            
            # Add config directory to path
            config_dir = os.path.join(os.path.dirname(__file__), "..", "..", "..", "config")
            if config_dir not in sys.path:
                sys.path.insert(0, config_dir)
            
            from config_loader import config as global_config
            
            # Cache the configuration
            self._config_cache = {
                "application": global_config.get("application", {}),
                "llm": global_config.get("llm", {}),
                "search": global_config.get("search", {}),
                "database": global_config.get("database", {}),
                "logging": global_config.get("logging", {}),
                "features": global_config.get("features", {}),
            }
            
            self._initialized = True
            logger.info("Configuration service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Set minimal default configuration
            self._config_cache = {
                "application": {
                    "technical_settings": {
                        "port": 8765,
                        "host": "0.0.0.0",
                        "debug": False
                    }
                },
                "llm": {
                    "technical_settings": {
                        "provider": "openai",
                        "model": "gpt-4.1-mini"
                    }
                },
                "search": {
                    "technical_settings": {
                        "default_engine": "searxng",
                        "max_results": 10
                    }
                },
                "database": {
                    "technical_settings": {
                        "path": "data/ldr.db"
                    }
                },
                "logging": {
                    "technical_settings": {
                        "level": "INFO"
                    }
                }
            }
            self._initialized = True
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a configuration value using dot notation.
        
        Args:
            key: Configuration key in dot notation (e.g., 'app.port')
            default: Default value if key is not found
            
        Returns:
            Configuration value or default
        """
        if not self._initialized:
            self._load_configuration()
        
        try:
            # Navigate through nested dictionary using dot notation
            keys = key.split('.')
            value = self._config_cache
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.warning(f"Error getting config key '{key}': {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        Set a configuration value.
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Navigate through nested dictionary and set value
            keys = key.split('.')
            config_dict = self._config_cache
            
            # Navigate to the parent of the target key
            for k in keys[:-1]:
                if k not in config_dict:
                    config_dict[k] = {}
                config_dict = config_dict[k]
            
            # Set the final value
            config_dict[keys[-1]] = value
            
            logger.debug(f"Set configuration key '{key}' to '{value}'")
            return True
            
        except Exception as e:
            logger.error(f"Error setting config key '{key}': {e}")
            return False
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration values.
        
        Returns:
            Complete configuration dictionary
        """
        if not self._initialized:
            self._load_configuration()
        
        return self._config_cache.copy()
    
    def reload(self) -> None:
        """Reload configuration from sources."""
        logger.info("Reloading configuration")
        self._config_cache.clear()
        self._initialized = False
        self._load_configuration()
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return {
            "path": self.get("database.technical_settings.path", "data/ldr.db"),
            "echo": self.get("database.technical_settings.echo", False),
            "pool_size": self.get("database.technical_settings.pool_size", 5),
            "max_overflow": self.get("database.technical_settings.max_overflow", 10)
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration."""
        return {
            "level": self.get("logging.technical_settings.level", "INFO"),
            "format": self.get("logging.technical_settings.format", "{time} | {level} | {message}"),
            "file_path": self.get("logging.technical_settings.file_path"),
            "rotation": self.get("logging.technical_settings.rotation", "1 day"),
            "retention": self.get("logging.technical_settings.retention", "7 days")
        }
    
    def get_search_config(self) -> Dict[str, Any]:
        """Get search configuration."""
        return {
            "default_engine": self.get("search.technical_settings.default_engine", "searxng"),
            "max_results": self.get("search.technical_settings.max_results", 10),
            "timeout": self.get("search.technical_settings.timeout", 30),
            "max_iterations": self.get("search.technical_settings.max_iterations", 3),
            "questions_per_iteration": self.get("search.technical_settings.questions_per_iteration", 3)
        }
    
    def get_llm_config(self) -> Dict[str, Any]:
        """Get LLM configuration."""
        return {
            "provider": self.get("llm.technical_settings.provider", "ollama"),
            "model": self.get("llm.technical_settings.model", "gemma:latest"),
            "temperature": self.get("llm.editorial_team_settings.creativity_vs_determinism", 0.7),
            "max_tokens": self.get("llm.technical_settings.max_tokens", 2000),
            "api_key": self.get("llm.technical_settings.openai_api_key"),
            "base_url": self.get("llm.technical_settings.base_url")
        }
    
    def validate(self) -> List[str]:
        """
        Validate configuration and return any issues.
        
        Returns:
            List of validation error messages
        """
        issues = []
        
        # Validate required settings
        required_settings = [
            ("application.technical_settings.port", "Application port"),
            ("llm.technical_settings.provider", "LLM provider"),
            ("search.technical_settings.default_engine", "Default search engine")
        ]
        
        for key, description in required_settings:
            if self.get(key) is None:
                issues.append(f"Missing required setting: {description} ({key})")
        
        # Validate port range
        port = self.get("application.technical_settings.port")
        if port and (not isinstance(port, int) or port < 1 or port > 65535):
            issues.append(f"Invalid port number: {port} (must be 1-65535)")
        
        return issues
