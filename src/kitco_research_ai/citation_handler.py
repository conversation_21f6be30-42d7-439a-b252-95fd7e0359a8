# citation_handler.py

from typing import Any, Dict, List, Union

from langchain_core.documents import Document

from .utilities.db_utils import get_db_setting
from .prompts.llm_prompts import prompt_manager


class CitationHandler:
    def __init__(self, llm):
        self.llm = llm

    def _create_documents(
        self, search_results: Union[str, List[Dict]], nr_of_links: int = 0
    ) -> List[Document]:
        """
        Convert search results to LangChain documents format and add index
        to original search results.
        """
        documents = []
        if isinstance(search_results, str):
            return documents

        for i, result in enumerate(search_results):
            if isinstance(result, dict):
                # Add index to the original search result dictionary
                result["index"] = str(i + nr_of_links + 1)

                content = result.get("full_content", result.get("snippet", ""))
                documents.append(
                    Document(
                        page_content=content,
                        metadata={
                            "source": result.get("link", f"source_{i + 1}"),
                            "title": result.get("title", f"Source {i + 1}"),
                            "index": i + nr_of_links + 1,
                        },
                    )
                )
        return documents

    def _format_sources(self, documents: List[Document]) -> str:
        """Format sources with numbers for citation."""
        sources = []
        for doc in documents:
            source_id = doc.metadata["index"]
            sources.append(f"[{source_id}] {doc.page_content}")
        return "\n\n".join(sources)

    def analyze_initial(
        self, query: str, search_results: Union[str, List[Dict]]
    ) -> Dict[str, Any]:

        documents = self._create_documents(search_results)
        formatted_sources = self._format_sources(documents)

        # Use centralized prompt manager
        prompt = prompt_manager.get_initial_citation_prompt(
            query=query,
            formatted_sources=formatted_sources
        )

        response = self.llm.invoke(prompt)
        if not isinstance(response, str):
            response = response.content
        return {"content": response, "documents": documents}

    def analyze_followup(
        self,
        question: str,
        search_results: Union[str, List[Dict]],
        previous_knowledge: str,
        nr_of_links: int,
    ) -> Dict[str, Any]:
        """Process follow-up analysis with citations."""
        documents = self._create_documents(search_results, nr_of_links=nr_of_links)
        formatted_sources = self._format_sources(documents)

        # Use centralized fact-checking prompt
        fact_check_prompt = prompt_manager.get_fact_check_prompt(
            previous_knowledge=previous_knowledge,
            formatted_sources=formatted_sources
        )

        if get_db_setting("general.enable_fact_checking", True):
            fact_check_response = self.llm.invoke(fact_check_prompt).content
        else:
            fact_check_response = ""

        # Use centralized follow-up citation prompt
        prompt = prompt_manager.get_followup_citation_prompt(
            question=question,
            previous_knowledge=previous_knowledge,
            formatted_sources=formatted_sources,
            fact_check_response=fact_check_response
        )

        response = self.llm.invoke(prompt)

        return {"content": response.content, "documents": documents}
