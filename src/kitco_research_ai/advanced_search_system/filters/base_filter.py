# src/kitco_research_ai/advanced_search_system/filters/base_filter.py
"""
Base class for search result filters.
"""

from abc import ABC, abstractmethod
from typing import Dict, List

from langchain_core.language_models.chat_models import BaseChatModel


class BaseFilter(ABC):
    """Abstract base class for all search result filters."""

    def __init__(self, model: BaseChatModel | None = None):
        """
        Initialize the filter.

        Args:
            model: The language model to use for relevance assessments
        """
        self.model = model

    @abstractmethod
    def filter_results(self, results: List[Dict], query: str, **kwargs) -> List[Dict]:
        """
        Filter search results by relevance to the query.

        Args:
            results: List of search result dictionaries
            query: The original search query
            **kwargs: Additional filter-specific parameters

        Returns:
            Filtered list of search results
        """
        pass
