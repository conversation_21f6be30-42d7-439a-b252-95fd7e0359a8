"""
Standard knowledge generator implementation.
"""

import logging
from datetime import datetime
from typing import List

from .base_knowledge import BaseKnowledgeGenerator
from ...prompts.llm_prompts import prompt_manager

logger = logging.getLogger(__name__)


class StandardKnowledge(BaseKnowledgeGenerator):
    """Standard knowledge generator implementation."""

    def generate_knowledge(
        self,
        query: str,
        context: str = "",
        current_knowledge: str = "",
        questions: List[str] = None,
    ) -> str:
        """Generate knowledge based on query and context."""
        now = datetime.now()
        current_time = now.strftime("%Y-%m-%d")

        logger.info("Generating knowledge...")

        # Use centralized prompt manager
        prompt = prompt_manager.get_knowledge_generation_prompt(
            query=query,
            current_time=current_time,
            context=context,
            current_knowledge=current_knowledge,
            questions=questions
        )

        response = self.model.invoke(prompt)
        knowledge = response.content

        logger.info("Generated knowledge successfully")
        return knowledge

    def generate_sub_knowledge(self, sub_query: str, context: str = "") -> str:
        """
        Generate knowledge for a sub-question.

        Args:
            sub_query: The sub-question to generate knowledge for
            context: Additional context for knowledge generation

        Returns:
            str: Generated knowledge for the sub-question
        """
        # Use centralized prompt manager
        prompt = prompt_manager.get_sub_knowledge_prompt(
            sub_query=sub_query,
            context=context
        )

        try:
            response = self.model.invoke(prompt)
            return response.content
        except Exception as e:
            logger.error(f"Error generating sub-knowledge: {str(e)}")
            return ""

    def generate(self, query: str, context: str) -> str:
        """Generate knowledge from the given query and context."""
        return self.generate_knowledge(query, context)

    def compress_knowledge(
        self, current_knowledge: str, query: str, section_links: list, **kwargs
    ) -> str:
        """
        Compress and summarize accumulated knowledge.

        Args:
            current_knowledge: The accumulated knowledge to compress
            query: The original research query
            section_links: List of source links
            **kwargs: Additional arguments

        Returns:
            str: Compressed knowledge
        """
        logger.info(
            f"Compressing knowledge for query: {query}. Original length: {len(current_knowledge)}"
        )

        # Use centralized prompt manager
        prompt = prompt_manager.get_knowledge_compression_prompt(
            current_knowledge=current_knowledge,
            query=query
        )

        try:
            response = self.model.invoke(prompt)
            compressed_knowledge = response.content
            logger.info(f"Compressed knowledge length: {len(compressed_knowledge)}")
            return compressed_knowledge
        except Exception as e:
            logger.error(f"Error compressing knowledge: {str(e)}")
            return current_knowledge  # Return original if compression fails

    def format_citations(self, links: List[str]) -> str:
        """
        Format source links into citations using IEEE style.

        Args:
            links: List of source links

        Returns:
            str: Formatted citations in IEEE style
        """
        if not links:
            return ""

        # Format each link as an IEEE citation
        citations = []
        for i, link in enumerate(links, 1):
            citations.append(f"[{i}] {link}")

        return "\n".join(citations)
