"""
Base class for all findings repositories.
Defines the common interface and shared functionality for different findings management approaches.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List

from langchain_core.language_models import BaseLLM

logger = logging.getLogger(__name__)


class BaseFindingsRepository(ABC):
    """Abstract base class for all findings repositories."""

    def __init__(self, model: BaseLLM):
        """
        Initialize the findings repository.

        Args:
            model: The language model to use for findings operations
        """
        self.model = model
        self.findings: Dict[str, List[str]] = {}

    @abstractmethod
    def add_finding(self, query: str, finding: Dict | str) -> None:
        """
        Add a finding to the repository.

        Args:
            query: The query associated with the finding
            finding: The finding to add
        """
        pass

    @abstractmethod
    def get_findings(self, query: str) -> List[str]:
        """
        Get findings for a query.

        Args:
            query: The query to get findings for

        Returns:
            List[str]: List of findings for the query
        """
        pass

    @abstractmethod
    def clear_findings(self, query: str) -> None:
        """
        Clear findings for a query.

        Args:
            query: The query to clear findings for
        """
        pass

    @abstractmethod
    def synthesize_findings(
        self,
        query: str,
        sub_queries: List[str],
        findings: List[str],
        accumulated_knowledge: str,
    ) -> str:
        """
        Synthesize findings from sub-queries into a final answer.

        Args:
            query: The original query
            sub_queries: List of sub-queries
            findings: List of findings for each sub-query
            accumulated_knowledge: Accumulated knowledge from previous findings
        Returns:
            str: Synthesized final answer
        """
        pass
