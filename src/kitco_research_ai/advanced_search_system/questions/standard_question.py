"""
Standard question generation implementation.
"""

import logging
from datetime import datetime
from typing import List

from .base_question import BaseQuestionGenerator
from ...prompts.llm_prompts import prompt_manager

logger = logging.getLogger(__name__)


class StandardQuestionGenerator(BaseQuestionGenerator):
    """Standard question generator."""

    def generate_questions(
        self,
        current_knowledge: str,
        query: str,
        questions_per_iteration: int = 2,
        questions_by_iteration: dict = None,
    ) -> List[str]:
        """Generate follow-up questions based on current knowledge."""
        now = datetime.now()
        current_time = now.strftime("%Y-%m-%d")
        questions_by_iteration = questions_by_iteration or {}

        logger.info("Generating follow-up questions...")

        # Use centralized prompt manager
        prompt = prompt_manager.get_standard_question_prompt(
            query=query,
            current_time=current_time,
            questions_per_iteration=questions_per_iteration,
            questions_by_iteration=questions_by_iteration,
            current_knowledge=current_knowledge
        )

        response = self.model.invoke(prompt)

        # Handle both string responses and responses with .content attribute
        response_text = ""
        if hasattr(response, "content"):
            response_text = response.content
        else:
            # Handle string responses
            response_text = str(response)

        questions = [
            q.replace("Q:", "").strip()
            for q in response_text.split("\n")
            if q.strip().startswith("Q:")
        ][:questions_per_iteration]

        logger.info(f"Generated {len(questions)} follow-up questions")

        return questions

    def generate_sub_questions(self, query: str, context: str = "") -> List[str]:
        """
        Generate sub-questions from a main query.

        Args:
            query: The main query to break down
            context: Additional context for question generation

        Returns:
            List[str]: List of generated sub-questions
        """
        prompt = f"""You are an expert at breaking down complex questions into simpler sub-questions.

Original Question: {query}

{context}

Break down the original question into 2-5 simpler sub-questions that would help answer the original question when answered in sequence.
Follow these guidelines:
1. Each sub-question should be specific and answerable on its own
2. Sub-questions should build towards answering the original question
3. For multi-hop or complex queries, identify the individual facts or entities needed
4. Ensure the sub-questions can be answered with separate searches

Format your response as a numbered list with ONLY the sub-questions, one per line:
1. First sub-question
2. Second sub-question
...

Only provide the numbered sub-questions, nothing else."""

        try:
            response = self.model.invoke(prompt)

            # Handle both string responses and responses with .content attribute
            content = ""
            if hasattr(response, "content"):
                content = response.content
            else:
                # Handle string responses
                content = str(response)

            # Parse sub-questions from the response
            sub_questions = []
            for line in content.strip().split("\n"):
                line = line.strip()
                if line and (line[0].isdigit() or line.startswith("-")):
                    # Extract sub-question from numbered or bulleted list
                    parts = line.split(".", 1) if "." in line else line.split(" ", 1)
                    if len(parts) > 1:
                        sub_question = parts[1].strip()
                        sub_questions.append(sub_question)

            # Limit to at most 5 sub-questions
            return sub_questions[:5]
        except Exception as e:
            logger.error(f"Error generating sub-questions: {str(e)}")
            return []
