"""
Pytest configuration and fixtures for Kitco Research AI tests.

This module provides common fixtures and configuration for all tests.
"""

import os
import tempfile
import pytest
from unittest.mock import Mock

# Add src to path for testing
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from kitco_research_ai.core.application import Application
from kitco_research_ai.core.container import Container, reset_container
from kitco_research_ai.services.interfaces import (
    IConfigService,
    IDatabaseService,
    ISearchService,
    IReportService,
    ILoggingService
)


@pytest.fixture(scope="function")
def temp_dir():
    """Create a temporary directory for tests."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture(scope="function")
def clean_container():
    """Provide a clean container for each test."""
    reset_container()
    container = Container()
    yield container
    reset_container()


@pytest.fixture(scope="function")
def mock_config_service():
    """Mock configuration service."""
    mock_service = Mock(spec=IConfigService)
    mock_service.get.return_value = None
    mock_service.set.return_value = True
    mock_service.get_all.return_value = {}
    mock_service.get_database_config.return_value = {
        "path": ":memory:",
        "echo": False,
        "pool_size": 1,
        "max_overflow": 0
    }
    mock_service.get_logging_config.return_value = {
        "level": "INFO",
        "format": "{time} | {level} | {message}"
    }
    mock_service.get_search_config.return_value = {
        "default_engine": "searxng",
        "max_results": 10,
        "timeout": 30
    }
    mock_service.get_llm_config.return_value = {
        "provider": "ollama",
        "model": "gemma:latest",
        "temperature": 0.7
    }
    return mock_service


@pytest.fixture(scope="function")
def mock_database_service():
    """Mock database service."""
    mock_service = Mock(spec=IDatabaseService)
    mock_service.initialize.return_value = None
    mock_service.health_check.return_value = True
    mock_service.execute_query.return_value = []
    mock_service.get_connection.return_value = Mock()
    mock_service.get_session.return_value = Mock()
    return mock_service


@pytest.fixture(scope="function")
def mock_search_service():
    """Mock search service."""
    mock_service = Mock(spec=ISearchService)
    mock_service.search.return_value = []
    mock_service.get_available_engines.return_value = ["searxng", "duckduckgo"]
    mock_service.validate_query.return_value = True
    return mock_service


@pytest.fixture(scope="function")
def mock_report_service():
    """Mock report service."""
    mock_service = Mock(spec=IReportService)
    mock_service.generate_report.return_value = "# Test Report"
    mock_service.get_report.return_value = None
    mock_service.save_report.return_value = True
    return mock_service


@pytest.fixture(scope="function")
def mock_logging_service():
    """Mock logging service."""
    mock_service = Mock(spec=ILoggingService)
    mock_service.initialize.return_value = None
    mock_service.log.return_value = None
    mock_service.get_logs.return_value = []
    return mock_service


@pytest.fixture(scope="function")
def test_application(clean_container, mock_config_service, mock_database_service):
    """Create a test application with mocked services."""
    container = clean_container
    
    # Register mock services
    container.register_instance(IConfigService, mock_config_service)
    container.register_instance(IDatabaseService, mock_database_service)
    
    app = Application(container)
    app._initialized = True  # Skip initialization for tests
    
    yield app
    
    app.shutdown()


@pytest.fixture(scope="function")
def sample_research_data():
    """Sample research data for testing."""
    return {
        "id": 1,
        "query": "test query",
        "status": "completed",
        "mode": "detailed",
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T01:00:00",
        "start_time": "2024-01-01T00:00:00",
        "end_time": "2024-01-01T01:00:00",
        "duration": 3600,
        "strategy": "iterdrag",
        "logs": [
            {
                "message": "Starting research",
                "level": "INFO",
                "timestamp": "2024-01-01T00:00:00",
                "metadata": {}
            }
        ],
        "resources": [
            {
                "url": "https://example.com",
                "title": "Example Resource",
                "content_type": "text/html",
                "relevance_score": 0.9,
                "metadata": {}
            }
        ]
    }


@pytest.fixture(scope="function")
def sample_config():
    """Sample configuration for testing."""
    return {
        "app.name": "Kitco Research AI",
        "app.port": 8765,
        "app.debug": False,
        "database.path": ":memory:",
        "logging.level": "INFO",
        "search.default_engine": "searxng",
        "search.max_results": 10,
        "llm.provider": "ollama",
        "llm.model": "gemma:latest",
        "llm.temperature": 0.7
    }


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    # Set test environment variables
    test_env = {
        "KRA_APP_DEBUG": "true",
        "KRA_DATABASE_PATH": ":memory:",
        "KRA_LOGGING_LEVEL": "DEBUG"
    }
    
    # Store original values
    original_env = {}
    for key, value in test_env.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original values
    for key, value in original_env.items():
        if value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = value


# Test markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "e2e: mark test as an end-to-end test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "requires_network: mark test as requiring network access"
    )
    config.addinivalue_line(
        "markers", "requires_llm: mark test as requiring LLM access"
    )
