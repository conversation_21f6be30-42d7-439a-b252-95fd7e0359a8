# ============================================================================
# Docker Ignore File for Kitco Research AI
# ============================================================================
# Excludes unnecessary files from Docker build context for faster builds
# and smaller image sizes
# ============================================================================

# Version control
.git
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/
env/
.env.local
.env.development
.env.test
.env.production

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Documentation and development files
docs/
*.md
!README.md
LICENSE
.editorconfig

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# Development tools
.mypy_cache/
.dmypy.json
dmypy.json
.pylint.d/

# Logs (will be mounted as volumes)
logs/
*.log

# Research outputs (will be mounted as volumes)
research_outputs/
!research_outputs/.gitkeep

# Data directory (will be mounted as volumes)
data/
!data/.gitkeep

# Temporary files
tmp/
temp/
.tmp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files (avoid recursion)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Development scripts
scripts/
!scripts/start_app.sh

# Configuration backups
config/backups/

# Security and sensitive files
.env*
!.env.example
*.key
*.pem
*.crt
secrets/
credentials/

# Node.js (if any frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter notebooks
.ipynb_checkpoints/
*.ipynb

# Large model files (should be downloaded at runtime)
models/
*.bin
*.safetensors
*.onnx

# Backup files
*.backup
*.bak
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z
