# ===============================================================================
# COMPREHENSIVE .GITIGNORE FOR KITCO RESEARCH AI
# ===============================================================================
# This file prevents sensitive data, credentials, and compromising files from
# being committed to version control. It includes comprehensive patterns for:
# - API keys and secrets (OpenAI, Anthropic, etc.)
# - Configuration files with credentials
# - Personal and development files
# - Temporary and cache files
# - Research outputs that may contain sensitive data
# - Database files and backups
# - Security-related files
# ===============================================================================

# Python
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.python-version
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment (always use .venv in project root)
.venv/
venv/
.venv.backup/
venv.backup/
ENV/
env/
.env
.pdm*
env.bak/
venv.bak/

# IDE and Editor files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.qodo/
.cursorrules
.project
.pydevproject
.settings/
.classpath
.factorypath
.spyderproject
.spyproject
.ropeproject
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
coverage.xml
*.cover
.coverage.*

# Project specific
temp_output.txt
temp_output_findings.txt
formatted_output.txt
Output.txt
research_outputs/
report.md
data/
.kitco_research_ai/

# Logs
*.log
logs/
pip-log.txt
pip-delete-this-directory.txt

# Database
research_history.db
*.db
*.db-journal
*.sqlite
*.sqlite3

# Data
*.pdf
.cache
*.jsonl
tmp/
temp/

# Tools
python-db/
js-db/
*.sarif
codeql_analysis_results.txt
.coverage

# API keys and secrets - CRITICAL SECURITY
.env*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production
.env.staging
.env.development

# Encrypted secrets storage
config/.secrets.enc
config/.secrets.enc.backup
*.secrets.enc
*.pem
*.key
*.crt
*.p12
*.pfx
*.jks
*.keystore
*_rsa
*_rsa.pub
*_dsa
*_dsa.pub
*_ecdsa
*_ecdsa.pub
*_ed25519
*_ed25519.pub
id_rsa*
id_dsa*
id_ecdsa*
id_ed25519*
known_hosts
authorized_keys

# Configuration files with potential secrets
config/app_config.json.production
config/app_config.json.staging
config/app_config.json.local
config/*_production.json
config/*_staging.json
config/*_local.json
config/secrets.json
config/credentials.json
config/api_keys.json
settings.json
local_settings.json
production_settings.json
staging_settings.json

# API and service credentials
*api_key*
*secret_key*
*secret*
*access_token*
*refresh_token*
*bearer_token*
*auth_token*
*session_token*
*jwt_token*
*oauth_token*
*client_secret*
*client_id.txt
*password*
*passwd*
*credentials*
*auth.json
*token.json
*secrets.txt
*keys.txt
*passwords.txt

# Cloud provider credentials
.aws/
.azure/
.gcp/
.google/
gcloud/
aws-credentials
azure-credentials
gcp-credentials
service-account*.json
*-service-account.json
google-credentials.json
aws-config
azure-config
gcp-config

# Database credentials and connection strings
database.conf
db.conf
*database_url*
*db_url*
*connection_string*
*conn_str*
.pgpass
.my.cnf
mysql.conf
postgresql.conf
redis.conf
mongodb.conf

# LLM and AI service keys
openai_api_key*
anthropic_api_key*
huggingface_token*
cohere_api_key*
replicate_api_token*
together_api_key*
groq_api_key*
mistral_api_key*
palm_api_key*
claude_api_key*
gpt_api_key*
llama_api_key*
ollama_config*

# OS specific files
.DS_Store
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Docker
.docker/
docker-compose.override.yml

# Node.js (for frontend assets)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Backup files
*.bak
*.backup
*.old
*.orig
*.tmp

# Large files and binaries
*.zip
*.tar.gz
*.tgz
*.rar
*.7z
*.doc
*.docx
*.xls
*.xlsx
*.ppt
*.pptx
*.bin
*.exe
*.dll
*.so
*.dylib
*.jar
*.war
*.ear
*.mp3
*.mp4
*.avi
*.mov
*.mkv
*.flv
*.wmv
*.wav
*.flac
*.ogg
*.aac
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.ico
*.svg
*.ttf
*.otf
*.woff
*.woff2
*.eot

# Exceptions - keep specific files/directories
!.gitignore
!.gitattributes
!.github/workflows/
!requirements.txt
!requirements-*.txt
!pyproject.toml
!README.md
!LICENSE
!MANIFEST.in
!src/
!app.py
!migrate_project.py
!update_search_engines.py
!Dockerfile*
!docker-compose*.yml
!.dockerignore

# Retain directory structure but ignore contents
!data/.gitkeep
!research_outputs/.gitkeep

**/.claude/settings.local.json

# Configuration system cleanup
# Old configuration files (if any remain)
complete_app_config.json
default_settings.json
master_settings.py

# Legacy script backups
scripts/*.backup
scripts/*.old

# Documentation backups
docs/*.backup
docs/*.old

# Security and sensitive development files
*.private
*.confidential
*.internal
*_private.*
*_confidential.*
*_internal.*
*_secret.*
*_sensitive.*
security.txt
security.json
security.yaml
security.yml
vulnerability_report.*
penetration_test.*
security_audit.*

# Development and debugging files
debug.log
debug_*.log
trace.log
trace_*.log
error.log
error_*.log
crash.log
crash_*.log
dump.*
core.*
*.dmp
*.stackdump
memory_dump.*
heap_dump.*
thread_dump.*

# Personal and user-specific files
*_personal.*
*_user.*
*_local.*
*_dev.*
*_test.*
my_*
user_*
personal_*
local_*
dev_*
test_*
.personal/
.user/
.local/
.dev/

# Temporary and work-in-progress files
*.wip
*.draft
*.scratch
*.playground
*_wip.*
*_draft.*
*_scratch.*
*_playground.*
*_temp.*
*_temporary.*
work_in_progress/
drafts/
scratch/
playground/
experiments/
sandbox/

# Research and analysis outputs (potentially sensitive)
research_outputs/*.pdf
research_outputs/*.docx
research_outputs/*.xlsx
research_outputs/*_confidential.*
research_outputs/*_internal.*
research_outputs/*_private.*
analysis_results/
research_data/
collected_data/
scraped_data/
extracted_data/

# Backup and archive files (may contain sensitive data)
*.backup.*
*_backup.*
backup_*
backups/
archives/
*.archive
*_archive.*
archive_*
*.snapshot
*_snapshot.*
snapshot_*
snapshots/

# Configuration backups (may contain secrets)
config/backups/*.json.backup
config/backups/*_production.*
config/backups/*_staging.*
config/backups/*_live.*
config/*.backup
config/*_backup.*
config/backup_*

# Session and state files
.session
.state
session.*
state.*
*_session.*
*_state.*
sessions/
states/

# Cache and temporary data (may contain sensitive info)
.cache/
cache/
*_cache/
*.cache
*_cache.*
.tmp/
tmp/
*_tmp/
*.tmp
*_tmp.*
temporary/
temp_files/

# IDE and editor temporary files (may contain sensitive data)
*.swp
*.swo
*.swn
*~
.#*
\#*\#
.*.swp
.*.swo
.*.swn
*.autosave
*_autosave.*
autosave_*

# Version control and merge files
*.orig
*.rej
*.patch
*.diff
*_BACKUP_*
*_BASE_*
*_LOCAL_*
*_REMOTE_*
.merge_file_*

# Profiling and performance data
*.prof
*.profile
*_profile.*
profile_*
profiling/
performance/
*.perf
*_perf.*
perf_*

# Testing artifacts (may contain sensitive test data)
test_data/
test_results/
test_outputs/
*_test_data.*
*_test_results.*
*_test_outputs.*
mock_data/
fixture_data/
sample_data/

# Documentation that might contain sensitive info
NOTES.md
NOTES.txt
TODO_PRIVATE.md
PRIVATE_README.md
INTERNAL_DOCS.md
*_PRIVATE.*
*_INTERNAL.*
*_CONFIDENTIAL.*
private_notes/
internal_docs/
confidential_docs/

# Network and monitoring files (may contain sensitive endpoints)
*.pcap
*.pcapng
network_trace.*
traffic_dump.*
packet_capture.*
monitoring_data/
metrics/
telemetry/
analytics/

# Deployment and infrastructure files (may contain secrets)
docker-compose.prod.yml
docker-compose.staging.yml
docker-compose.local.yml
Dockerfile.prod
Dockerfile.staging
.dockerignore.local
kubernetes/
k8s/
helm/
terraform/
ansible/
deployment/
infrastructure/
*.tf
*.tfvars
*.tfstate
*.tfstate.backup

# CI/CD and automation files (may contain secrets)
.github/workflows/*_prod.yml
.github/workflows/*_staging.yml
.gitlab-ci.local.yml
.travis.local.yml
.circleci/config.local.yml
jenkins/
buildkite/
.buildkite/
pipeline/
pipelines/

# Monitoring and alerting configurations
prometheus/
grafana/
alertmanager/
monitoring/
alerts/
dashboards/
*.alert
*_alert.*
alert_*

# Compliance and audit files
audit_log.*
compliance_report.*
gdpr_data.*
privacy_audit.*
data_inventory.*
retention_policy.*
compliance/
audit/
gdpr/
privacy/

# Machine learning and AI model files (potentially sensitive)
models/
*.model
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.pt
*.pth
*.bin
model_weights/
checkpoints/
training_data/
validation_data/
embeddings/
vectors/

# Research-specific sensitive files
research_notes/
interview_transcripts/
survey_data/
participant_data/
study_results/
analysis_notebooks/
*.research
*_research.*
research_*

# Financial and business data
financial_data/
revenue_data/
customer_data/
user_data/
analytics_data/
business_intelligence/
*.financial
*_financial.*
financial_*

# Legal and contractual documents
contracts/
agreements/
legal_docs/
nda/
*.contract
*_contract.*
contract_*
*.agreement
*_agreement.*
agreement_*

# Email and communication exports
emails/
messages/
communications/
*.mbox
*.eml
*.msg
chat_logs/
slack_export/
discord_export/

# Browser and application data
*.history
browser_data/
application_data/
user_profiles/
preferences/
bookmarks/
cookies/
sessions/
local_storage/

# System and hardware information
system_info.*
hardware_info.*
device_info.*
network_info.*
*.sysinfo
*_sysinfo.*
sysinfo_*

# ===============================================================================
# SECURITY REMINDER
# ===============================================================================
# ALWAYS REVIEW FILES BEFORE COMMITTING!
# - Check for hardcoded API keys, passwords, or tokens
# - Verify no sensitive data in configuration files
# - Ensure no personal or confidential information
# - Use environment variables for secrets in production
# - Regularly audit and update this .gitignore file
# ===============================================================================