#!/usr/bin/env python3
"""
Centralized Configuration Management Script
==========================================

Manage the centralized configuration system with backup/restore capabilities.

Usage:
    python scripts/config_centralized.py status
    python scripts/config_centralized.py reset
    python scripts/config_centralized.py backup
    python scripts/config_centralized.py validate
    python scripts/config_centralized.py migrate
"""

import argparse
import json
import shutil
import sys
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from loguru import logger


class CentralizedConfigManager:
    """Manager for centralized configuration system"""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.current_config = self.root_dir / "config" / "app_config.json"
        self.default_config = self.root_dir / "config" / "default_config.json"
        self.backup_dir = self.root_dir / "config" / "backups"
        
        # Ensure backup directory exists
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def status(self):
        """Show status of configuration files"""
        print("=" * 60)
        print("CENTRALIZED CONFIGURATION STATUS")
        print("=" * 60)
        
        # Check current config
        if self.current_config.exists():
            try:
                with open(self.current_config) as f:
                    current_data = json.load(f)
                print(f"✅ Current Config: {self.current_config}")
                print(f"   Version: {current_data.get('_info', {}).get('version', 'Unknown')}")
                print(f"   Size: {self.current_config.stat().st_size} bytes")
            except Exception as e:
                print(f"❌ Current Config: {self.current_config} (CORRUPTED: {e})")
        else:
            print(f"❌ Current Config: {self.current_config} (MISSING)")
        
        # Check default config
        if self.default_config.exists():
            try:
                with open(self.default_config) as f:
                    default_data = json.load(f)
                print(f"✅ Default Config: {self.default_config}")
                print(f"   Version: {default_data.get('_info', {}).get('version', 'Unknown')}")
                print(f"   Size: {self.default_config.stat().st_size} bytes")
            except Exception as e:
                print(f"❌ Default Config: {self.default_config} (CORRUPTED: {e})")
        else:
            print(f"❌ Default Config: {self.default_config} (MISSING)")
        
        # Check backups
        current_backups = list(self.backup_dir.glob("app_config_*.json"))
        old_backups = list(self.backup_dir.glob("old_*.json"))
        all_backups = current_backups + old_backups

        if all_backups:
            print(f"📁 Backups: {len(all_backups)} found in {self.backup_dir}")
            if current_backups:
                print(f"   Current config backups: {len(current_backups)}")
                for backup in sorted(current_backups)[-2:]:  # Show last 2
                    print(f"     - {backup.name}")
            if old_backups:
                print(f"   Migration backups: {len(old_backups)}")
                for backup in sorted(old_backups)[-2:]:  # Show last 2
                    print(f"     - {backup.name}")
        else:
            print(f"📁 Backups: None found in {self.backup_dir}")
        
        print("=" * 60)
    
    def reset(self):
        """Reset current config to defaults"""
        print("🔄 Resetting configuration to defaults...")
        
        if not self.default_config.exists():
            print(f"❌ Default config not found: {self.default_config}")
            return False
        
        try:
            # Create backup first
            self.backup()
            
            # Copy default to current
            shutil.copy2(self.default_config, self.current_config)
            print(f"✅ Configuration reset to defaults")
            print(f"   Current config restored from: {self.default_config}")
            return True
            
        except Exception as e:
            print(f"❌ Error resetting configuration: {e}")
            return False
    
    def backup(self):
        """Create backup of current configuration"""
        if not self.current_config.exists():
            print(f"❌ Current config not found: {self.current_config}")
            return False
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = self.backup_dir / f"app_config_{timestamp}.json"
            
            shutil.copy2(self.current_config, backup_file)
            print(f"✅ Configuration backed up to: {backup_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return False
    
    def validate(self):
        """Validate configuration files"""
        print("🔍 Validating configuration files...")
        
        valid = True
        
        # Validate current config
        if self.current_config.exists():
            try:
                with open(self.current_config) as f:
                    json.load(f)
                print(f"✅ Current config is valid JSON")
            except json.JSONDecodeError as e:
                print(f"❌ Current config has invalid JSON: {e}")
                valid = False
            except Exception as e:
                print(f"❌ Error reading current config: {e}")
                valid = False
        else:
            print(f"❌ Current config missing: {self.current_config}")
            valid = False
        
        # Validate default config
        if self.default_config.exists():
            try:
                with open(self.default_config) as f:
                    json.load(f)
                print(f"✅ Default config is valid JSON")
            except json.JSONDecodeError as e:
                print(f"❌ Default config has invalid JSON: {e}")
                valid = False
            except Exception as e:
                print(f"❌ Error reading default config: {e}")
                valid = False
        else:
            print(f"❌ Default config missing: {self.default_config}")
            valid = False
        
        if valid:
            print("✅ All configuration files are valid")
        else:
            print("❌ Configuration validation failed")
        
        return valid
    
    def migrate(self):
        """Migrate from old configuration system"""
        print("🔄 Migrating from old configuration system...")
        
        old_configs = [
            self.root_dir / "config" / "complete_app_config.json",
            self.root_dir / "default_settings.json"
        ]
        
        migrated = False
        
        for old_config in old_configs:
            if old_config.exists():
                print(f"📁 Found old config: {old_config}")
                
                # Create backup of old config
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = self.backup_dir / f"old_{old_config.name}_{timestamp}.json"
                
                try:
                    shutil.copy2(old_config, backup_file)
                    print(f"✅ Old config backed up to: {backup_file}")
                    migrated = True
                except Exception as e:
                    print(f"❌ Error backing up old config: {e}")
        
        if migrated:
            print("✅ Migration completed - old configs backed up")
            print("💡 You can now safely remove old config files")
        else:
            print("ℹ️  No old configuration files found to migrate")
        
        return migrated


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="Centralized Configuration Management",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        "command",
        choices=["status", "reset", "backup", "validate", "migrate"],
        help="Command to execute"
    )
    
    args = parser.parse_args()
    
    manager = CentralizedConfigManager()
    
    if args.command == "status":
        manager.status()
    elif args.command == "reset":
        manager.reset()
    elif args.command == "backup":
        manager.backup()
    elif args.command == "validate":
        manager.validate()
    elif args.command == "migrate":
        manager.migrate()


if __name__ == "__main__":
    main()
