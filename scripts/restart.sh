#!/bin/bash

# Kitco Research AI - Fresh Restart Script
# This script performs a clean restart of the application

set -e  # Exit on any error

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Change to project root directory
PROJECT_ROOT="$(find_project_root)"
cd "$PROJECT_ROOT"

echo "🔄 Kitco Research AI - Fresh Restart"
echo "===================================="
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

# Function to find and kill processes on port
kill_port_processes() {
    local port=$1
    echo "🔍 Checking for processes on port $port..."

    # Find processes using the port
    local pids=$(lsof -ti:$port 2>/dev/null || true)

    if [ -n "$pids" ]; then
        echo "⚠️  Found processes on port $port: $pids"
        echo "🛑 Stopping processes..."
        kill $pids 2>/dev/null || true
        sleep 2

        # Force kill if still running
        local remaining=$(lsof -ti:$port 2>/dev/null || true)
        if [ -n "$remaining" ]; then
            echo "🔨 Force killing remaining processes..."
            kill -9 $remaining 2>/dev/null || true
        fi
        echo "✅ Port $port cleared"
    else
        echo "✅ Port $port is free"
    fi
}

# Stop any running instances
echo "🛑 Stopping any running instances..."
kill_port_processes 8765
kill_port_processes 5000
kill_port_processes 5001
kill_port_processes 5002
kill_port_processes 8080
echo ""

# Check virtual environment
echo "🐍 Checking virtual environment..."
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "🔧 Please run the initial setup first:"
    echo "   ./setup"
    exit 1
fi
echo "✅ Virtual environment found"
echo ""

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate
echo "✅ Virtual environment activated"
echo ""

# Detect environment and choose appropriate requirements file
echo "🔍 Detecting environment..."
REQUIREMENTS_FILE="config/requirements.txt"
ENV_NAME="Standard"

# Check if development tools are available to determine environment
if python -c "import pytest, black, mypy" 2>/dev/null; then
    REQUIREMENTS_FILE="config/requirements-dev.txt"
    ENV_NAME="Development"
    echo "📚 Development environment detected"
elif [ -f "config/requirements-prod.txt" ] && [ "$(wc -l < config/requirements-prod.txt)" -lt "$(wc -l < config/requirements.txt)" ]; then
    # If production requirements exist and are smaller, we might be in production
    if ! python -c "import pytest" 2>/dev/null; then
        REQUIREMENTS_FILE="config/requirements-prod.txt"
        ENV_NAME="Production"
        echo "🏭 Production environment detected"
    fi
fi

echo "🎯 Using $ENV_NAME environment ($REQUIREMENTS_FILE)"
echo ""

# Check if dependencies are installed
echo "📦 Checking dependencies..."
if ! python -c "import flask, langchain, playwright" 2>/dev/null; then
    echo "⚠️  Some dependencies missing, reinstalling..."
    echo "📄 Installing from: $REQUIREMENTS_FILE"
    pip install -r "$REQUIREMENTS_FILE"
    echo "✅ Dependencies reinstalled"
else
    echo "✅ Core dependencies check passed"

    # Additional check for development tools if in dev environment
    if [ "$ENV_NAME" = "Development" ]; then
        if ! python -c "import pytest, black, mypy" 2>/dev/null; then
            echo "⚠️  Development tools missing, reinstalling..."
            pip install -r "$REQUIREMENTS_FILE"
            echo "✅ Development dependencies reinstalled"
        else
            echo "✅ Development tools check passed"
        fi
    fi
fi
echo ""

# Check Playwright browsers
echo "🌐 Checking Playwright browsers..."
if ! python -c "from playwright.sync_api import sync_playwright; sync_playwright().start().chromium.launch()" 2>/dev/null; then
    echo "⚠️  Playwright browsers missing, reinstalling..."
    playwright install
    echo "✅ Playwright browsers reinstalled"
else
    echo "✅ Playwright browsers available"
fi
echo ""

# Clean up any temporary files
echo "🧹 Cleaning up temporary files..."
find . -name "*.pyc" -delete 2>/dev/null || true
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".pytest_cache" -type d -exec rm -rf {} + 2>/dev/null || true
echo "✅ Cleanup completed"
echo ""

# Check database
echo "🗄️  Checking database..."
if [ -f "data/ldr.db" ]; then
    echo "✅ Database found"
else
    echo "⚠️  Database not found, initializing..."
    python src/kitco_research_ai/setup_data_dir.py
    echo "✅ Database initialized"
fi
echo ""

# Get port preference
PORT=${1:-8765}

echo "🎉 Fresh Restart Complete!"
echo "=========================="
echo ""
echo "✅ Environment: Ready"
echo "✅ Dependencies: Verified"
echo "✅ Browsers: Available"
echo "✅ Database: Ready"
echo "✅ Cleanup: Completed"
echo ""
echo "🚀 Starting application on port $PORT..."
echo "🌐 Access at: http://localhost:$PORT"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Start the application
python app.py --port=$PORT
