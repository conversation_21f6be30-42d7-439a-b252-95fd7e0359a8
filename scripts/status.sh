#!/bin/bash

# Kitco Research AI - Status Check Script
# This script checks the current status of the project setup

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Change to project root directory
PROJECT_ROOT="$(find_project_root)"
cd "$PROJECT_ROOT"

echo "📊 Kitco Research AI - Status Check"
echo "==================================="
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

# Check virtual environment
echo "🐍 Virtual Environment:"
if [ -d ".venv" ]; then
    echo "   ✅ Virtual environment exists (.venv/)"
    if [ -f ".venv/bin/activate" ]; then
        echo "   ✅ Activation script found"
    else
        echo "   ❌ Activation script missing"
    fi
elif [ -d "venv" ]; then
    echo "   ⚠️  Old virtual environment found (venv/)"
    echo "   🔧 Consider migrating to .venv: ./scripts/update_requirements.sh"
else
    echo "   ❌ Virtual environment not found"
    echo "   🔧 Run: ./scripts/setup.sh"
fi
echo ""

# Check configuration
echo "⚙️  Configuration:"
if [ -f ".env" ]; then
    echo "   ✅ Environment file exists"

    # Check for API keys
    if grep -q "^OPENAI_API_KEY=" .env 2>/dev/null; then
        echo "   ✅ OpenAI API key configured"
    else
        echo "   ⚠️  OpenAI API key not configured"
    fi

    if grep -q "^BRAVE_API_KEY=" .env 2>/dev/null; then
        echo "   ✅ Brave API key configured"
    else
        echo "   ⚠️  Brave API key not configured"
    fi
else
    echo "   ❌ Environment file missing"
    echo "   🔧 Run: ./setup.sh"
fi
echo ""

# Check database
echo "🗄️  Database:"
if [ -f "data/ldr.db" ]; then
    echo "   ✅ Database file exists"
    db_size=$(du -h data/ldr.db | cut -f1)
    echo "   📊 Database size: $db_size"
else
    echo "   ❌ Database not found"
    echo "   🔧 Run: ./setup.sh or ./start_app.sh"
fi
echo ""

# Check if application is running
echo "🌐 Application Status:"
running_ports=""
for port in 8765 5000 5001 5002 8080; do
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        running_ports="$running_ports $port"
    fi
done

if [ -n "$running_ports" ]; then
    echo "   ✅ Application running on port(s):$running_ports"
    for port in $running_ports; do
        echo "   🌐 Access at: http://localhost:$port"
    done
else
    echo "   ⚠️  Application not running"
    echo "   🚀 Start with: ./start_app.sh"
fi
echo ""

# Check dependencies (if .venv exists)
if [ -d ".venv" ]; then
    echo "📦 Dependencies:"
    source .venv/bin/activate 2>/dev/null

    # Detect environment type
    ENV_TYPE="Standard"
    if python -c "import pytest, black, mypy" 2>/dev/null; then
        ENV_TYPE="Development"
    elif ! python -c "import pytest" 2>/dev/null; then
        ENV_TYPE="Production"
    fi
    echo "   🎯 Environment: $ENV_TYPE"

    # Core dependencies
    if python -c "import flask" 2>/dev/null; then
        echo "   ✅ Flask installed"
    else
        echo "   ❌ Flask missing"
    fi

    if python -c "import langchain" 2>/dev/null; then
        echo "   ✅ LangChain installed"
    else
        echo "   ❌ LangChain missing"
    fi

    if python -c "import playwright" 2>/dev/null; then
        echo "   ✅ Playwright installed"
    else
        echo "   ❌ Playwright missing"
    fi

    if python -c "import openai" 2>/dev/null; then
        echo "   ✅ OpenAI installed"
    else
        echo "   ❌ OpenAI missing"
    fi

    # Development tools (if development environment)
    if [ "$ENV_TYPE" = "Development" ]; then
        echo "   📚 Development Tools:"
        if python -c "import pytest" 2>/dev/null; then
            echo "      ✅ pytest (testing)"
        else
            echo "      ❌ pytest missing"
        fi

        if python -c "import black" 2>/dev/null; then
            echo "      ✅ black (formatting)"
        else
            echo "      ❌ black missing"
        fi

        if python -c "import mypy" 2>/dev/null; then
            echo "      ✅ mypy (type checking)"
        else
            echo "      ❌ mypy missing"
        fi
    fi

    # Check requirements files
    echo "   📄 Requirements Files:"
    if [ -f "config/requirements.txt" ]; then
        echo "      ✅ requirements.txt ($(wc -l < config/requirements.txt) packages)"
    else
        echo "      ❌ requirements.txt missing"
    fi

    if [ -f "config/requirements-dev.txt" ]; then
        echo "      ✅ requirements-dev.txt (development)"
    else
        echo "      ⚠️  requirements-dev.txt missing"
    fi

    if [ -f "config/requirements-prod.txt" ]; then
        echo "      ✅ requirements-prod.txt (production)"
    else
        echo "      ⚠️  requirements-prod.txt missing"
    fi

    deactivate 2>/dev/null || true
    echo ""
fi

# Summary
echo "📋 Summary:"
if [ -d ".venv" ] && [ -f ".env" ] && [ -f "data/ldr.db" ]; then
    echo "   ✅ Project setup complete"
    if [ -n "$running_ports" ]; then
        echo "   ✅ Application running"
        echo "   🎉 Ready to use!"
    else
        echo "   🚀 Ready to start: ./start_app.sh"
    fi
else
    echo "   ⚠️  Setup incomplete"
    echo "   🔧 Run setup: ./setup"
fi
