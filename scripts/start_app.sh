#!/bin/bash

# Kitco Research AI - Startup Script
# This script activates the virtual environment and starts the web application

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Change to project root directory
PROJECT_ROOT="$(find_project_root)"
cd "$PROJECT_ROOT"

echo "🚀 Starting Kitco Research AI..."
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "🔧 Please run the initial setup first:"
    echo "   ./scripts/setup.sh"
    exit 1
fi

# Check if setup is complete
if [ ! -f ".env" ]; then
    echo "❌ Configuration not found!"
    echo "🔧 Please run the initial setup first:"
    echo "   ./setup"
    exit 1
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate
echo "✅ Virtual environment activated"
echo ""

# Set default port if not specified
PORT=${1:-8765}

# Check if port is in use
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Port $PORT is already in use!"
    echo "🔄 Try using a different port:"
    echo "   ./start_app.sh 5002"
    echo ""
    echo "🔄 Or use the restart script to clean up:"
    echo "   ./restart.sh"
    exit 1
fi

echo "📡 Starting web server on port $PORT..."
echo "🌐 Access the application at: http://localhost:$PORT"
echo "⏹️  Press Ctrl+C to stop the server"
echo ""

# Start the application
python app.py --port=$PORT
