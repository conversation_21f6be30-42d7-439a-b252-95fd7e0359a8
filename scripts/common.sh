#!/bin/bash

# ============================================================================
# Kitco Research AI - Common Functions
# ============================================================================
# Shared functions used across multiple scripts
# Source this file in other scripts: source "$(dirname "$0")/common.sh"
# ============================================================================

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[1]}")" && pwd)"
    
    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi
    
    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi
    
    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done
    
    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Function to ensure we're in project root
ensure_project_root() {
    local project_root="$(find_project_root)"
    cd "$project_root"
    echo "$project_root"
}

# Function to check if virtual environment exists
check_venv() {
    if [ -d ".venv" ]; then
        echo ".venv"
        return 0
    elif [ -d "venv" ]; then
        echo "venv"
        return 0
    else
        return 1
    fi
}

# Function to activate virtual environment
activate_venv() {
    local venv_dir="$(check_venv)"
    if [ $? -eq 0 ]; then
        source "$venv_dir/bin/activate"
        return 0
    else
        echo "❌ Virtual environment not found!" >&2
        echo "🔧 Please run the initial setup first: ./scripts/setup.sh" >&2
        return 1
    fi
}

# Function to detect environment type
detect_environment() {
    if ! activate_venv; then
        echo "unknown"
        return 1
    fi
    
    # Check if development tools are available
    if python -c "import pytest, black, mypy" 2>/dev/null; then
        echo "dev"
    elif ! python -c "import pytest" 2>/dev/null; then
        # If pytest is not available, likely production
        echo "prod"
    else
        echo "standard"
    fi
}

# Function to get appropriate requirements file based on environment
get_requirements_file() {
    local env_type="$(detect_environment)"
    case "$env_type" in
        "dev")
            echo "config/requirements-dev.txt"
            ;;
        "prod")
            echo "config/requirements-prod.txt"
            ;;
        "standard")
            echo "config/requirements.txt"
            ;;
        *)
            echo "config/requirements.txt"
            ;;
    esac
}

# Function to check if port is in use
check_port() {
    local port=${1:-8765}
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill processes on a port
kill_port_processes() {
    local port=${1:-8765}
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "🔄 Killing processes on port $port..."
        echo "$pids" | xargs kill -9 2>/dev/null || true
        sleep 2
        return 0
    else
        return 1
    fi
}

# Function to validate Python version
check_python_version() {
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)" 2>/dev/null; then
        echo "❌ Python 3.10 or higher is required" >&2
        echo "   Current version: $(python3 --version 2>/dev/null || echo 'Not found')" >&2
        return 1
    fi
    return 0
}

# Function to show colored output
print_status() {
    local status="$1"
    local message="$2"
    
    case "$status" in
        "success"|"✅")
            echo "✅ $message"
            ;;
        "error"|"❌")
            echo "❌ $message" >&2
            ;;
        "warning"|"⚠️")
            echo "⚠️  $message"
            ;;
        "info"|"ℹ️")
            echo "ℹ️  $message"
            ;;
        *)
            echo "$status $message"
            ;;
    esac
}

# Function to create directory if it doesn't exist
ensure_directory() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "success" "Created directory: $dir"
    fi
}

# Function to backup file if it exists
backup_file() {
    local file="$1"
    local backup_suffix="${2:-.backup}"
    
    if [ -f "$file" ]; then
        cp "$file" "$file$backup_suffix"
        print_status "success" "Backed up $file to $file$backup_suffix"
        return 0
    else
        return 1
    fi
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for service to be ready
wait_for_service() {
    local url="$1"
    local timeout="${2:-30}"
    local interval="${3:-2}"
    local elapsed=0
    
    echo "⏳ Waiting for service at $url..."
    
    while [ $elapsed -lt $timeout ]; do
        if curl -f "$url" >/dev/null 2>&1; then
            print_status "success" "Service is ready at $url"
            return 0
        fi
        sleep $interval
        elapsed=$((elapsed + interval))
    done
    
    print_status "error" "Service at $url did not become ready within ${timeout}s"
    return 1
}

# Export functions for use in other scripts
export -f find_project_root
export -f ensure_project_root
export -f check_venv
export -f activate_venv
export -f detect_environment
export -f get_requirements_file
export -f check_port
export -f kill_port_processes
export -f check_python_version
export -f print_status
export -f ensure_directory
export -f backup_file
export -f command_exists
export -f wait_for_service
