#!/bin/bash

# ============================================================================
# Kitco Research AI - Requirements Update Script
# ============================================================================
# This script helps migrate from old requirements system to new comprehensive
# requirements system with multiple environment support
# ============================================================================

set -e  # Exit on any error

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Change to project root directory
PROJECT_ROOT="$(find_project_root)"
cd "$PROJECT_ROOT"

echo "🔄 Kitco Research AI - Requirements System Update"
echo "================================================="
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

# Check if new requirements files exist
if [ ! -f "config/requirements.txt" ] || [ ! -f "config/requirements-dev.txt" ] || [ ! -f "config/requirements-prod.txt" ]; then
    echo "❌ Error: New requirements files not found"
    echo "   Please ensure the new requirements system is properly set up"
    exit 1
fi

echo "✅ New requirements system detected"
echo ""

# Backup old virtual environment if it exists
if [ -d ".venv" ]; then
    echo "📦 Backing up existing virtual environment (.venv)..."
    if [ -d ".venv.backup" ]; then
        rm -rf .venv.backup
    fi
    mv .venv .venv.backup
    echo "✅ Virtual environment backed up to .venv.backup"
    echo ""
elif [ -d "venv" ]; then
    echo "📦 Found old virtual environment (venv)..."
    echo "🔄 Migrating from venv/ to .venv/"
    if [ -d ".venv.backup" ]; then
        rm -rf .venv.backup
    fi
    mv venv .venv.backup
    echo "✅ Old virtual environment backed up to .venv.backup"
    echo ""
fi

# Ask user which environment they want
echo "🎯 Choose your environment setup:"
echo "1) Development (recommended for local development)"
echo "   - Includes all dependencies plus development tools"
echo "   - Testing frameworks, linting, debugging tools"
echo "   - File: config/requirements-dev.txt"
echo ""
echo "2) Production (minimal dependencies)"
echo "   - Only essential dependencies for production"
echo "   - Optimized for security and performance"
echo "   - File: config/requirements-prod.txt"
echo ""
echo "3) Standard (all features, no dev tools)"
echo "   - All application features without development tools"
echo "   - File: config/requirements.txt"
echo ""

while true; do
    read -p "Enter your choice (1-3): " choice
    case $choice in
        1)
            ENVIRONMENT="dev"
            REQUIREMENTS_FILE="config/requirements-dev.txt"
            ENV_NAME="Development"
            break
            ;;
        2)
            ENVIRONMENT="prod"
            REQUIREMENTS_FILE="config/requirements-prod.txt"
            ENV_NAME="Production"
            break
            ;;
        3)
            ENVIRONMENT="standard"
            REQUIREMENTS_FILE="config/requirements.txt"
            ENV_NAME="Standard"
            break
            ;;
        *)
            echo "❌ Invalid choice. Please enter 1, 2, or 3."
            ;;
    esac
done

echo ""
echo "🎯 Selected: $ENV_NAME Environment"
echo "📄 Requirements: $REQUIREMENTS_FILE"
echo ""

# Create new virtual environment
echo "🐍 Creating new virtual environment..."
python3 -m venv .venv
echo "✅ Virtual environment created"
echo ""

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate
echo "✅ Virtual environment activated"
echo ""

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip
echo "✅ Pip upgraded"
echo ""

# Install dependencies
echo "📚 Installing dependencies ($ENV_NAME)..."
echo "📄 Using: $REQUIREMENTS_FILE"
echo "⏳ This may take several minutes..."
pip install -r "$REQUIREMENTS_FILE"
echo "✅ Dependencies installed"
echo ""

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
echo "⏳ This may take several minutes..."
playwright install
echo "✅ Playwright browsers installed"
echo ""

# Run security check if in development environment
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "🔒 Running security check..."
    if python scripts/security_check.py --quiet; then
        echo "✅ Security check passed"
    else
        echo "⚠️  Security check found issues (see above)"
    fi
    echo ""
fi

# Test the installation
echo "🧪 Testing installation..."
if python -c "import flask, langchain, playwright, openai" 2>/dev/null; then
    echo "✅ Core dependencies test passed"
else
    echo "❌ Core dependencies test failed"
    echo "   Some required packages may not be installed correctly"
fi

if [ "$ENVIRONMENT" = "dev" ]; then
    if python -c "import pytest, black, mypy" 2>/dev/null; then
        echo "✅ Development tools test passed"
    else
        echo "❌ Development tools test failed"
    fi
fi
echo ""

# Show completion message
echo "🎉 Requirements Update Complete!"
echo "==============================="
echo ""
echo "✅ Environment: $ENV_NAME"
echo "✅ Virtual environment: .venv/ (new)"
echo "✅ Backup: .venv.backup/ (old environment)"
echo "✅ Dependencies: $REQUIREMENTS_FILE"
echo "✅ Playwright browsers: Installed"
echo ""

# Show environment-specific information
case $ENVIRONMENT in
    "dev")
        echo "🚀 Development Environment Ready!"
        echo "================================"
        echo ""
        echo "📚 Available development tools:"
        echo "   • pytest - Run tests: pytest"
        echo "   • black - Format code: black src/"
        echo "   • mypy - Type checking: mypy src/"
        echo "   • pylint - Linting: pylint src/kitco_research_ai/"
        echo "   • pip-audit - Security audit: pip-audit"
        echo ""
        ;;
    "prod")
        echo "🏭 Production Environment Ready!"
        echo "==============================="
        echo ""
        echo "⚠️  Production Notes:"
        echo "   • Minimal dependencies for security"
        echo "   • Development tools not available"
        echo "   • Use production WSGI server for deployment"
        echo ""
        ;;
    "standard")
        echo "📦 Standard Environment Ready!"
        echo "============================="
        echo ""
        echo "📝 All features available without development tools"
        echo ""
        ;;
esac

echo "🚀 Next Steps:"
echo "   1. Start the application: ./scripts/start_app.sh"
echo "   2. Check status: ./scripts/status.sh"
echo "   3. View help: ./scripts/help.sh"
echo ""
echo "📖 Documentation:"
echo "   • Requirements guide: config/README-requirements.md"
echo "   • Complete documentation: docs/README.md"
echo "   • Production guide: docs/PRODUCTION.md"
echo ""
echo "🗑️  Cleanup:"
echo "   • Remove backup when satisfied: rm -rf .venv.backup"
echo ""

echo "✨ Update completed successfully! ✨"
