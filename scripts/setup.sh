#!/bin/bash

# Kitco Research AI - Initial Setup Script
# This script performs the complete initial setup for the project

set -e  # Exit on any error

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo "❌ Error: Could not find project root directory" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Change to project root directory
PROJECT_ROOT="$(find_project_root)"
cd "$PROJECT_ROOT"

echo "🚀 Kitco Research AI - Initial Setup"
echo "===================================="
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

# Parse command line arguments
ENVIRONMENT="dev"
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --production)
            ENVIRONMENT="prod"
            shift
            ;;
        --development)
            ENVIRONMENT="dev"
            shift
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            echo "❌ Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Show help if requested
if [ "$HELP" = true ]; then
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --env ENV          Set environment (dev, prod, standard) [default: dev]"
    echo "  --development      Use development environment (includes dev tools)"
    echo "  --production       Use production environment (minimal dependencies)"
    echo "  --help, -h         Show this help message"
    echo ""
    echo "Environments:"
    echo "  dev        Development environment with all tools (requirements-dev.txt)"
    echo "  prod       Production environment with minimal dependencies (requirements-prod.txt)"
    echo "  standard   Standard environment with all features (requirements.txt)"
    echo ""
    echo "Examples:"
    echo "  $0                    # Development setup (default)"
    echo "  $0 --development      # Development setup (explicit)"
    echo "  $0 --production       # Production setup"
    echo "  $0 --env standard     # Standard setup"
    exit 0
fi

# Determine requirements file based on environment
case $ENVIRONMENT in
    "dev")
        REQUIREMENTS_FILE="config/requirements-dev.txt"
        ENV_NAME="Development"
        ENV_DESC="Includes all dependencies plus development tools (testing, linting, debugging)"
        ;;
    "prod")
        REQUIREMENTS_FILE="config/requirements-prod.txt"
        ENV_NAME="Production"
        ENV_DESC="Minimal dependencies for production deployment"
        ;;
    "standard")
        REQUIREMENTS_FILE="config/requirements.txt"
        ENV_NAME="Standard"
        ENV_DESC="All features without development tools"
        ;;
    *)
        echo "❌ Invalid environment: $ENVIRONMENT"
        echo "Valid environments: dev, prod, standard"
        exit 1
        ;;
esac

echo "🎯 Environment: $ENV_NAME"
echo "📝 Description: $ENV_DESC"
echo "📄 Requirements: $REQUIREMENTS_FILE"
echo ""

# Check Python version
echo "📋 Checking Python version..."
python3 --version
if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)"; then
    echo "❌ Python 3.10 or higher is required"
    exit 1
fi
echo "✅ Python version check passed"
echo ""

# Create virtual environment
echo "🐍 Creating virtual environment..."
if [ -d ".venv" ]; then
    echo "⚠️  Virtual environment already exists, skipping creation"
else
    python3 -m venv .venv
    echo "✅ Virtual environment created"
fi
echo ""

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source .venv/bin/activate
echo "✅ Virtual environment activated"
echo ""

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip
echo "✅ Pip upgraded"
echo ""

# Install dependencies
echo "📚 Installing Python dependencies ($ENV_NAME)..."
echo "📄 Using: $REQUIREMENTS_FILE"
echo "⏳ This may take several minutes..."
pip install -r "$REQUIREMENTS_FILE"
echo "✅ Dependencies installed"
echo ""

# Install Playwright browsers
echo "🌐 Installing Playwright browsers..."
echo "⏳ This may take several minutes..."
playwright install
echo "✅ Playwright browsers installed"
echo ""

# Create environment file
echo "⚙️  Setting up configuration..."
if [ ! -f ".env" ]; then
    cp src/kitco_research_ai/defaults/.env.template .env
    echo "✅ Environment file created from template"
else
    echo "⚠️  Environment file already exists, skipping"
fi
echo ""

# Initialize data directory
echo "🗄️  Initializing data directory..."
python src/kitco_research_ai/setup_data_dir.py
echo "✅ Data directory initialized"
echo ""

# Make startup scripts executable
echo "🔧 Setting up scripts..."
chmod +x scripts/start_app.sh
chmod +x scripts/restart.sh
chmod +x scripts/status.sh
chmod +x scripts/help.sh
echo "✅ Scripts configured"
echo ""

# Run security check if in development environment
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "🔒 Running security check..."
    if python scripts/security_check.py --quiet; then
        echo "✅ Security check passed"
    else
        echo "⚠️  Security check found issues (see above)"
    fi
    echo ""
fi

# Show environment-specific completion message
echo "🎉 Setup Complete ($ENV_NAME Environment)!"
echo "=========================================="
echo ""
echo "✅ Virtual environment: .venv/"
echo "✅ Dependencies: $REQUIREMENTS_FILE"
echo "✅ Playwright browsers: Installed"
echo "✅ Configuration: .env file created"
echo "✅ Database: Initialized"
echo "✅ Scripts: Ready"
if [ "$ENVIRONMENT" = "dev" ]; then
    echo "✅ Development tools: Available"
fi
echo ""

# Show next steps based on environment
case $ENVIRONMENT in
    "dev")
        echo "🚀 Development Environment Ready!"
        echo "================================"
        echo ""
        echo "📚 Available development tools:"
        echo "   • pytest - Testing framework"
        echo "   • black - Code formatter"
        echo "   • mypy - Type checker"
        echo "   • pylint - Code linter"
        echo "   • pip-audit - Security scanner"
        echo ""
        echo "🔧 Development commands:"
        echo "   ./scripts/start_app.sh          # Start development server"
        echo "   pytest                          # Run tests"
        echo "   black src/                      # Format code"
        echo "   mypy src/                       # Type checking"
        echo "   pip-audit                       # Security audit"
        echo ""
        ;;
    "prod")
        echo "🚀 Production Environment Ready!"
        echo "==============================="
        echo ""
        echo "⚠️  Production Notes:"
        echo "   • Minimal dependencies installed for security"
        echo "   • Development tools not available"
        echo "   • Configure proper secrets management"
        echo "   • Use production WSGI server (gunicorn)"
        echo ""
        echo "🔧 Production commands:"
        echo "   ./scripts/start_app.sh          # Start application"
        echo "   gunicorn app:app                # Production server"
        echo ""
        ;;
    "standard")
        echo "🚀 Standard Environment Ready!"
        echo "============================="
        echo ""
        echo "📝 Standard installation includes all features"
        echo "   without development tools"
        echo ""
        echo "🔧 Commands:"
        echo "   ./scripts/start_app.sh          # Start application"
        echo ""
        ;;
esac

echo "🔧 Configuration:"
echo "   Edit .env file to add your API keys"
echo "   See config/README-requirements.md for dependency info"
echo ""
echo "📖 Documentation:"
echo "   docs/README.md                  # Complete guide"
echo "   docs/PRODUCTION.md              # Production deployment"
echo "   config/README-requirements.md   # Requirements guide"
